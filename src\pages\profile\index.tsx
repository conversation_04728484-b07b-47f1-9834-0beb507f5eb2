import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import useAuthStore from '@/store/authStore';
import { domain, token } from '@/lib/utils';
import axios from 'axios';
import { JobCompany } from '@/types';
import Spinner from '@/components/Spinner';
import { toast } from '@/hooks/use-toast';

interface Form {
    first_name: string;
    last_name: string;
    email: string;
    mobile_number: string;
    company: number | undefined;
    company_name: string | undefined;
    designation: number | undefined;
    designation_name: string | undefined;
    address: string;
    pincode: string;
    image: string | ArrayBuffer | null | undefined;
}

const Profile: React.FC = () => {
    const { user } = useAuthStore((state) => state);
    const userImage: string = domain + "/" + user?.image;
    const login = useAuthStore((state) => state.login);

    const [loading, setLoading] = useState<boolean>(false);

    const [companies, setCompanies] = useState<JobCompany[]>([]);
    const [designations, setDesignations] = useState<JobCompany[]>([]);
    const [image, setImage] = useState<File | null>(null);
    const [formInput, setFormInput] = useState<Form>({
        first_name: "",
        last_name: "",
        email: "",
        mobile_number: "",
        company: user?.company,
        company_name: user?.company_name,
        designation: user?.designation,
        designation_name: user?.designation_name,
        address: "",
        pincode: "",
        image: user?.company_logo,
    });

    const [errors, setErrors] = useState<any>({}); // For storing error messages

    useEffect(() => {
        const token = localStorage.getItem('kloutMapAppToken');
        if (!token) {
            return;
        }

        try {
            axios.post(`${domain}/mapping/api/profile`, {}, {
                headers: {
                    "Authorization": `Bearer ${token}`,
                    "Content-Type": "application/json"
                },
            }).then(res => {
                const user = res.data.user;

                setFormInput({
                    first_name: user.first_name,
                    last_name: user.last_name,
                    email: user.email,
                    mobile_number: user.mobile_number,
                    company: 0,
                    company_name: user.company_name,
                    designation: 0,
                    designation_name: user.designation_name,
                    address: user.address || "",
                    pincode: user.pincode || "",
                    image: user.company_logo || undefined,
                });
            });
        } catch (error) {
            console.error('Failed to fetch user profile:', error);
        }
    }, []);

    useEffect(() => {
        axios.get(`${domain}/api/job-titles`).then(res => setDesignations(res.data.data));
        axios.get(`${domain}/api/companies`).then(res => setCompanies(res.data.data));
    }, [setCompanies, setDesignations]);

    useEffect(() => {
        const companyNumber = companies.find((company) => company.name === formInput.company_name);
        const designationNumber = designations.find((designation) => designation.name === formInput.designation_name);
        setFormInput({
            ...formInput,
            company: companyNumber?.id || 439,
            designation: designationNumber?.id || 252
        });
    }, [formInput.company_name, formInput.designation_name]);

    const validateForm = () => {
        let isValid = true;
        let validationErrors: any = {};

        // Check for empty fields
        if (!formInput.first_name) {
            validationErrors.first_name = 'First Name is required';
            isValid = false;
        }
        if (!formInput.last_name) {
            validationErrors.last_name = 'Last Name is required';
            isValid = false;
        }
        if (!formInput.email) {
            validationErrors.email = 'Email is required';
            isValid = false;
        } else if (!/\S+@\S+\.\S+/.test(formInput.email)) {
            validationErrors.email = 'Email is invalid';
            isValid = false;
        }
        if (!formInput.mobile_number) {
            validationErrors.mobile_number = 'Phone number is required';
            isValid = false;
        } else if (!/^\d{10}$/.test(formInput.mobile_number)) {
            validationErrors.mobile_number = 'Phone number must be 10 digits';
            isValid = false;
        }
        if (!formInput.company_name) {
            validationErrors.company_name = 'Company is required';
            isValid = false;
        }
        if (!formInput.designation_name) {
            validationErrors.designation_name = 'Designation is required';
            isValid = false;
        }
        if (!formInput.address) {
            validationErrors.address = 'Address is required';
            isValid = false;
        }
        if (!formInput.pincode) {
            validationErrors.pincode = 'Pincode is required';
            isValid = false;
        }

        setErrors(validationErrors);
        return isValid;
    };

    const handleSubmit = async (e: any) => {
        e.preventDefault();

        if (!validateForm()) return; // If form is not valid, don't proceed

        if (!token) return;

        setLoading(true);

        const form = new FormData();

        Object.keys(formInput).forEach((key) => {
            const value = formInput[key as keyof Form];

            // Only append if value is a valid string or Blob, convert to string if necessary
            if (value !== undefined && value !== null) {
                // Ensure the value is a string (or Blob if needed)
                form.append(key, String(value)); // Convert to string
            }
        });

        if (image) {
            form.append("image", image);
        }

        try {
            const res = await axios.post(`${domain}/api/updateprofile`, form, {
                headers: {
                    "Content-Type": "multipart/form-data",
                    Authorization: `Bearer ${token}`
                }
            });

            login(token);
            if (res.data.status === 200) {
                toast({
                    title: res.data.message,
                    className: "bg-green-800 text-white",
                });
            } else {
                toast({
                    title: res.data.message,
                    className: "bg-red-800 text-white",
                });
            }

        } catch (error: any) {
            toast({
                title: error.message,
                className: "bg-green-800 text-white",
            });
        } finally {
            setLoading(false);
        }
    };

    // Handle image selection and validation
    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const fileType = file.type.split('/')[1];

            // Check if the file is an image and of valid type (jpg, jpeg, png)
            if (['jpg', 'jpeg', 'png'].includes(fileType)) {
                const reader = new FileReader();
                reader.onloadend = () => {
                    setFormInput({ ...formInput, image: reader.result }); // Set the image preview in formInput
                };
                reader.readAsDataURL(file);
                setImage(file);
            } else {
                toast({
                    title: 'Only JPG, JPEG, and PNG files are allowed.',
                    className: "bg-red-800 text-white",
                });
            }
        }
    };

    if (loading) {
        return <Spinner />;
    }

    return (
        <div className='min-w-full'>
            {/* Form Wrapper */}
            <div className='flex flex-col gap-5 border-2 w-full max-w-3xl p-5 lg:p-10 mx-auto bg-background rounded-lg'>
                {/* Image */}
                <div className='flex gap-5 items-center'>
                    <img
                        className='w-60 h-60 border-2 border-brandPrimary object-cover object-center mx-auto p-1 rounded-lg'
                        // src={formInput.image ? formInput.image : avatarImage}
                        src={userImage}
                        alt="user image" />
                    <span>Or</span>
                    <button className='px-5 py-2 rounded-lg relative bg-brandPrimary duration-300 hover:bg-brandPrimaryDark text-white'>Upload New Image
                        <Input type='file' className='absolute w-full h-full top-0 left-0 opacity-0 cursor-pointer' onChange={handleImageChange} />
                    </button>
                </div>

                {/* Name Wrapper Div */}
                <div className='flex flex-col min-[460px]:flex-row gap-5 w-full'>
                    {/* First Name */}
                    <div className='w-full'>
                        <Label htmlFor="first_name" className='text-primary'>First Name <span className='text-destructive'>*</span></Label>
                        <Input
                            type='text'
                            value={formInput.first_name}
                            onChange={(e) => setFormInput({ ...formInput, first_name: e.target.value })}
                            placeholder='Your First Name'
                            className='w-full'
                        />
                        {errors.first_name && <p className='text-red-500 text-sm'>{errors.first_name}</p>}
                    </div>

                    {/* Last Name */}
                    <div className='w-full'>
                        <Label htmlFor="last_name" className='text-primary'>Last Name <span className='text-destructive'>*</span></Label>
                        <Input
                            type='text'
                            value={formInput.last_name}
                            onChange={(e) => setFormInput({ ...formInput, last_name: e.target.value })}
                            placeholder='Your Last Name'
                            className='w-full'
                        />
                        {errors.last_name && <p className='text-red-500 text-sm'>{errors.last_name}</p>}
                    </div>
                </div>

                {/* Email and Phone NO. Wrapper Div */}
                <div className='flex flex-col min-[460px]:flex-row gap-5 w-full'>
                    {/* Email */}
                    <div className='w-full'>
                        <Label htmlFor="email" className='text-primary'>Email <span className='text-destructive'>*</span></Label>
                        <Input
                            type='email'
                            value={formInput.email}
                            onChange={(e) => setFormInput({ ...formInput, email: e.target.value })}
                            placeholder='Your Email'
                            className='w-full'
                        />
                        {errors.email && <p className='text-red-500 text-sm'>{errors.email}</p>}
                    </div>

                    {/* Phone No. */}
                    <div className='w-full'>
                        <Label htmlFor="mobile_number" className='text-primary'>Phone No. <span className='text-destructive'>*</span></Label>
                        <Input
                            type='tel'
                            value={formInput.mobile_number}
                            onChange={(e) => setFormInput({ ...formInput, mobile_number: e.target.value })}
                            placeholder='Your Phone No.'
                            className='w-full'
                        />
                        {errors.mobile_number && <p className='text-red-500 text-sm'>{errors.mobile_number}</p>}
                    </div>
                </div>

                {/* Company and Designation Wrapper Div */}
                <div className='flex flex-col min-[460px]:flex-row gap-5 w-full'>
                    {/* Company */}
                    <div className='w-full'>
                        <Label htmlFor="company_name" className='text-primary'>Company <span className='text-destructive'>*</span></Label>
                        <Select defaultValue={formInput.company_name} onValueChange={(value) => setFormInput({ ...formInput, company_name: value })}>
                            <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select Company" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectGroup>
                                    <SelectLabel>Companies</SelectLabel>
                                    {
                                        companies.map((company) => (
                                            <SelectItem key={company.id} value={company.name}>{company.name}</SelectItem>
                                        ))
                                    }
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                        {errors.company_name && <p className='text-red-500 text-sm'>{errors.company_name}</p>}
                    </div>

                    {/* Designation */}
                    <div className='w-full'>
                        <Label htmlFor="designation_name" className='text-primary'>Designation <span className='text-destructive'>*</span></Label>
                        <Select defaultValue={formInput.designation_name} onValueChange={(value) => setFormInput({ ...formInput, designation_name: value })}>
                            <SelectTrigger className="w-full">
                                <SelectValue placeholder="Select Designation" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectGroup>
                                    <SelectLabel>Designations</SelectLabel>
                                    {
                                        designations.map((designation) => (
                                            <SelectItem key={designation.id} value={designation.name}>{designation.name}</SelectItem>
                                        ))
                                    }
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                        {errors.designation_name && <p className='text-red-500 text-sm'>{errors.designation_name}</p>}
                    </div>
                </div>

                {/* Address and Pincode Wrapper Div */}
                <div className='flex flex-col min-[460px]:flex-row gap-5 w-full'>
                    {/* Address */}
                    <div className='w-full'>
                        <Label htmlFor="address" className='text-primary'>Address <span className='text-destructive'>*</span></Label>
                        <Input
                            type='text'
                            value={formInput.address}
                            onChange={(e) => setFormInput({ ...formInput, address: e.target.value })}
                            placeholder='Your Address'
                            className='w-full'
                        />
                        {errors.address && <p className='text-red-500 text-sm'>{errors.address}</p>}
                    </div>

                    {/* Pincode */}
                    <div className='w-full'>
                        <Label htmlFor="pincode" className='text-primary'>Pincode <span className='text-destructive'>*</span></Label>
                        <Input
                            type='number'
                            value={formInput.pincode}
                            onChange={(e) => setFormInput({ ...formInput, pincode: e.target.value })}
                            placeholder='Your Pincode'
                            className='w-full'
                        />
                        {errors.pincode && <p className='text-red-500 text-sm'>{errors.pincode}</p>}
                    </div>
                </div>

                <button onClick={handleSubmit} className='px-5 py-2 max-w-60 w-full mx-auto rounded-lg bg-brandPrimary hover:bg-brandPrimaryDark'>
                    Update
                </button>
            </div>
        </div>
    )
}

export default Profile;
