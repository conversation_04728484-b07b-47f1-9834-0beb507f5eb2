import Spinner from '@/components/Spinner';
import { domain2, token } from '@/lib/utils';
import { AppUser } from '@/types';
import axios from 'axios';
import React, { useEffect, useState } from 'react';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from '@/components/ui/input';

const ViewExcel: React.FC = () => {
  const [appUsers, setAppUsers] = useState<AppUser[]>([]); // Original data (unfiltered)
  const [filterData, setFilterData] = useState<AppUser[]>([]); // Filtered data
  const [loading, setLoading] = useState<boolean>(false);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [itemsPerPage, setItemPerPage] = useState<number>(10);

  // Filter States
  const [nameFilter, setNameFilter] = useState<string>("");
  const [designationFilter, setDesignationFilter] = useState<string>("");
  const [companyFilter, setCompanyFilter] = useState<string>("");
  const [emailFilter, setEmailFilter] = useState<string>("");
  const [industryFilter, setIndustryFilter] = useState<string>("");
  const [employeeCount, setEmployeeCount] = useState<string>("");
  const [countryFilter, setCountryFilter] = useState<string>("");
  const [jobFunctionFilter, setJobFunctionFilter] = useState<string>("");

  // Fetching data from the API
  useEffect(() => {
    setLoading(true);
    axios.get(`${domain2}/api/mapping/v1/premium-data/all`, {
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      }
    }).then(res => {
      if (res.data.status) {
        setAppUsers(res.data.data); // Set the original data
        setFilterData(res.data.data); // Set the filtered data (initially unfiltered)
      }
    }).catch(error => {
      console.log("Something went wrong", error);
    }).finally(() => setLoading(false));
  }, []);

  // Filter users based on name and designation
  useEffect(() => {
    const filteredUsers = appUsers.filter(user => {
      const fullName = `${user.first_name} ${user.last_name}`.toLowerCase();

      const nameMatch = fullName.includes(nameFilter.toLowerCase());
      const designationMatch = user.job_title?.toLowerCase().includes(designationFilter.toLowerCase());
      const companyMatch = user.company_name.toLowerCase().includes(companyFilter.toLowerCase());
      const emailMatch = user.email.toLowerCase().includes(emailFilter.toLowerCase());
      const industryMatch = user.industry.toLowerCase().includes(industryFilter.toLowerCase());
      const employeeCountMatch = user.employee_size.toLowerCase().includes(employeeCount.toLowerCase());
      const countryMatch = user.country.toLowerCase().includes(countryFilter.toLowerCase());
      const jobFunctionMatch = user.job_function.toLowerCase().includes(jobFunctionFilter.toLowerCase());
      return nameMatch && designationMatch && companyMatch && emailMatch && industryMatch && employeeCountMatch && countryMatch && jobFunctionMatch;
    });

    // Update the filterData state with filtered users
    setFilterData(filteredUsers);
    setPageNumber(1);
  }, [nameFilter, designationFilter, companyFilter, emailFilter, employeeCount, industryFilter, appUsers, countryFilter, jobFunctionFilter]);

  // Logic for pagination
  const indexOfLastItem = pageNumber * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentAppUsers = filterData.slice(indexOfFirstItem, indexOfLastItem);

  const totalPages = Math.ceil(filterData.length / itemsPerPage);

  if (loading) {
    return <Spinner />;
  }

  return (
    <div>
      <div className='my-5 flex gap-3 overflow-x-scroll p-1'>
        <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemPerPage(Number(value))}>
          <SelectTrigger className="w-fit">
            <SelectValue placeholder="Show Rows" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Select Rows</SelectLabel>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>

        {/* Filter By Name */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setNameFilter(e.target.value)}
            placeholder="Filter by name"
          />
        </div>

        {/* Filter By Designation */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setDesignationFilter(e.target.value)}
            placeholder="Filter by designation"
          />
        </div>

        {/* Filter By Company */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setCompanyFilter(e.target.value)}
            placeholder="Filter by Company"
          />
        </div>

        {/* Filter By Email */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setEmailFilter(e.target.value)}
            placeholder="Filter by Email"
          />
        </div>

        {/* Filter By Industry */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setIndustryFilter(e.target.value)}
            placeholder="Filter by Industry"
          />
        </div>

        {/* Filter By Employee Count */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setEmployeeCount(e.target.value)}
            placeholder="Filter by Size"
          />
        </div>

        {/* Filter By Country */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setCountryFilter(e.target.value)}
            placeholder="Filter by Location"
          />
        </div>

        {/* Filter By Job Function */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setJobFunctionFilter(e.target.value)}
            placeholder="Filter by Job Function"
          />
        </div>
        
      </div>

      <p className='text-right text-popover-foreground opacity-50'>Total Users: {appUsers.length}</p>

      <Table className='text-base w-full'>
        <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
          <TableRow>
            <TableHead className='text-white'>S.No</TableHead>
            <TableHead className='text-white'>Name</TableHead>
            <TableHead className='text-white'>Designation</TableHead>
            <TableHead className="text-white">Company Name</TableHead>
            <TableHead className="text-white">Industry</TableHead>
            <TableHead className="text-white">Email</TableHead>
            <TableHead className="text-white">Phone No.</TableHead>
            <TableHead className="text-white">Alternalte Phone No.</TableHead>
            <TableHead className="text-white">Website</TableHead>
            <TableHead className="text-white">Employee Size</TableHead>
            <TableHead className="text-white">Company Turn Over</TableHead>
            <TableHead className="text-white">LinkedIn Page Link</TableHead>
            <TableHead className="text-white">Location</TableHead>
            <TableHead className="text-white">Job Function</TableHead>
            {/* <TableHead className="text-white">Action</TableHead> */}
          </TableRow>
        </TableHeader>
        <TableBody>
          {currentAppUsers?.map((data: AppUser, index: number) => (
            <TableRow key={data._id}>
              <TableCell>{index + 1}</TableCell>
              <TableCell>{data.first_name + " " + data.last_name}</TableCell>
              <TableCell>{data.job_title || <p className='opacity-30'>NA</p>}</TableCell>
              <TableCell>{data.company_name || <p className='opacity-30'>NA</p>}</TableCell>
              <TableCell>{data.industry || <p className='opacity-30'>NA</p>}</TableCell>
              <TableCell>{data.email || <p className='opacity-30'>NA</p>}</TableCell>
              <TableCell>{data.phone_number || <p className='opacity-30'>NA</p>}</TableCell>
              <TableCell>{data.alternate_mobile_number || <p className='opacity-30'>NA</p>}</TableCell>
              <TableCell>{data.website ? <a href={data.website} target='_blank' className='underline text-brandPrimary'>{data.company_name}</a> : <p className='opacity-30'>NA</p>}</TableCell>
              <TableCell>{data.employee_size || <p className='opacity-30'>NA</p>}</TableCell>
              <TableCell>{data.company_turn_over || <p className='opacity-30'>NA</p>}</TableCell>
              <TableCell>{data.linkedin_page_link ? <a href={data.linkedin_page_link} target='_blank' className='underline text-brandPrimary'>{data.company_name + " LinkedIn"}</a> : <p className='opacity-30'>NA</p>}</TableCell>
              <TableCell>{data.country || <p className='opacity-30'>NA</p>}</TableCell>
              <TableCell>{data.job_function || <p className='opacity-30'>NA</p>}</TableCell>
              {/* <TableCell>{data.__v}</TableCell> */}
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination */}
      <div className='mt-10'>
        <Pagination>
          <PaginationContent>
            {/* Previous Button */}
            <PaginationItem className="cursor-pointer">
              <PaginationPrevious
                onClick={() => setPageNumber((prev) => Math.max(1, prev - 1))}
                className={pageNumber === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === 1}
              >
                Prev
              </PaginationPrevious>
            </PaginationItem>

            {/* First page */}
            <PaginationItem className="cursor-pointer">
              <PaginationLink
                onClick={() => setPageNumber(1)}
                isActive={pageNumber === 1}
              >
                1
              </PaginationLink>
            </PaginationItem>

            {/* Ellipsis before the current range */}
            {pageNumber > 3 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Dynamic Page Numbers */}
            {Array.from({ length: 3 }, (_, index) => {
              const page = pageNumber - 1 + index;
              if (page > 1 && page < totalPages) {
                return (
                  <PaginationItem className="cursor-pointer" key={page}>
                    <PaginationLink
                      isActive={pageNumber === page}
                      onClick={() => setPageNumber(page)}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              }
              return null;
            })}

            {/* Ellipsis after the current range */}
            {pageNumber < totalPages - 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Last page */}
            {totalPages > 1 && (
              <PaginationItem className="cursor-pointer">
                <PaginationLink
                  onClick={() => setPageNumber(totalPages)}
                  isActive={pageNumber === totalPages}
                >
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            )}

            {/* Next Button */}
            <PaginationItem className="cursor-pointer">
              <PaginationNext
                onClick={() => setPageNumber((prev) => Math.min(totalPages, prev + 1))}
                className={pageNumber === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === totalPages}
              >
                Next
              </PaginationNext>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
};

export default ViewExcel;