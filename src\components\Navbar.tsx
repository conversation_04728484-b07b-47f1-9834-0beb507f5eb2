import React, { useEffect, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ModeToggle } from './mode-toggle';

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
    SidebarContent,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarMenu,
} from "@/components/ui/sidebar";

import {
    Sheet,
    SheetContent,
    SheetTrigger,
} from "@/components/ui/sheet";

import { Link, useLocation } from 'react-router-dom';
import { links } from '@/constants';
import useAuthStore from '@/store/authStore';
import { avatarImage, domain } from '@/lib/utils';
import { LogOut, UserRoundPen } from 'lucide-react';

const Navbar: React.FC = () => {

    const logout = useAuthStore((state) => state.logout);
    const user = useAuthStore((state) => state.user);
    const userImage: string = domain + "/" + user?.image;
    const location = useLocation();
    const [heading, setHeading] = useState<string | undefined>("Dashboard");
    const [isOpen, setIsOpen] = useState<boolean>(false);

    useEffect(() => {
        const page = links.find(link => link.url === location.pathname);

        if (page?.url === "/") {
            setHeading("Dashboard");
        } else if (page) {
            setHeading(page.title);
        } else {
            // Format location.pathname into a readable heading
            const formattedHeading = location.pathname
                .replace(/^\//, '') // Remove leading slash
                .replace(/-/g, ' ') // Replace hyphens with spaces
                .split('/')         // Split by slashes
                .map(segment => segment.charAt(0).toUpperCase() + segment.slice(1)) // Capitalize each segment
                .join(' / ');       // Join with a separator (optional)

            setHeading(formattedHeading);
        }
    }, [location]);


    const handleLogout = () => {
        logout();
    }

    return (
        <header className='w-full p-3 2xl:p-5 dark:bg-popover bg-white !sticky top-0 !z-50 flex justify-between items-center'>

            <h3 className='text-2xl font-bold'>{heading}</h3>

            <nav className='hidden md:flex items-center gap-5'>
                <ModeToggle />

                <DropdownMenu>
                    <DropdownMenuTrigger className='border-2 border-brandPrimary rounded-full p-[2px]'>
                        <Avatar>
                            <AvatarImage src={user?.image ? userImage : avatarImage} />
                            <AvatarFallback>CN</AvatarFallback>
                        </Avatar>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className='w-40 mr-3 2xl:mr-5'>
                        <DropdownMenuLabel>My Account</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <Link to="/profile">
                            <DropdownMenuItem className='cursor-pointer'>Profile</DropdownMenuItem>
                        </Link>
                        {/* <DropdownMenuItem className='cursor-pointer'>Billing</DropdownMenuItem>
                        <DropdownMenuItem className='cursor-pointer'>Team</DropdownMenuItem>
                        <DropdownMenuItem className='cursor-pointer'>Subscription</DropdownMenuItem> */}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={handleLogout} className='text-red-500 hover:!text-red-600 cursor-pointer'>Logout</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>

            </nav>

            <nav className='md:hidden flex'>

                <Sheet open={isOpen} onOpenChange={setIsOpen}>
                    <SheetTrigger>
                        <Avatar>
                            <AvatarImage src={user?.image ? userImage : avatarImage} />
                            <AvatarFallback>CN</AvatarFallback>
                        </Avatar>
                    </SheetTrigger>

                    <SheetContent className='p-3 bg-brandPrimary text-white overflow-y-scroll'>
                        {/* <SheetHeader>
                            <SheetTitle>Klout Mapping Application</SheetTitle>
                            <SheetDescription>
                                This action cannot be undone. This will permanently delete your account
                                and remove your data from our servers.
                            </SheetDescription>
                        </SheetHeader> */}

                        <SidebarContent>
                            <SidebarGroup className="overflow-y-scroll p-0">
                                <SidebarGroupLabel className="py-6 2xl:py-8 px-0 grid place-content-center font-light text-lg">Klout Club || Map</SidebarGroupLabel>
                                <SidebarGroupContent className="mt-3 p-0">
                                    <SidebarMenu>

                                        {
                                            links.map((item) => (
                                                <Link
                                                    key={item.url}
                                                    to={item.url}
                                                    onClick={() => setIsOpen(false)}
                                                    title={!isOpen ? item.title : ""}
                                                    className={`flex links-center py-4 rounded hover:bg-black/20 ${location.pathname === item.url ? "bg-black/20" : ""
                                                        }`}
                                                >
                                                    <item.icon size={24} className="w-16" /> {/* Icon */}
                                                    {isOpen && <span className="font-medium">{item.title}</span>} {/* Title */}
                                                </Link>
                                            ))
                                        }

                                        <Link
                                            to={"/profile"}
                                            onClick={() => setIsOpen(false)}
                                            title={!isOpen ? "Profile" : ""}
                                            className={`flex links-center py-4 rounded hover:bg-black/20 ${location.pathname === "/profile" ? "bg-black/20" : ""
                                                }`}
                                        >
                                            <UserRoundPen size={24} className="w-16" /> {/* Icon */}
                                            {isOpen && <span className="font-medium">Profile</span>} {/* Title */}
                                        </Link>

                                        <button
                                            onClick={handleLogout}
                                            className={`flex links-center py-4 rounded hover:bg-black/20 text-white cursor-pointer`}
                                        >
                                            <LogOut size={24} className="w-16" /> {/* Icon */}
                                            {isOpen && <span className="font-medium">Logout</span>} {/* Title */}
                                        </button>

                                    </SidebarMenu>
                                </SidebarGroupContent>
                            </SidebarGroup>
                        </SidebarContent>
                    </SheetContent>
                </Sheet>


            </nav>
        </header>
    )
}

export default Navbar;