import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarTrigger,
} from "@/components/ui/sidebar";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

import { Link, useLocation } from "react-router-dom";
import { useState } from "react";
import { dropdownGroups, regularLinks } from "@/constants";

export function AppSidebar() {
  const location = useLocation();
  const [isOpen, setIsOpen] = useState<boolean>(true);

  const isActiveInGroup = (items: typeof dropdownGroups[0]['items']) => {
    return items.some(item => location.pathname === item.url);
  };

  return (
    <Sidebar collapsible="icon" className="bg-brandPrimary text-white">
      <SidebarTrigger onClick={() => setIsOpen((prev) => !prev)} className="absolute rounded-full drop-shadow-xl p-2 shadow bg-white -right-4 bottom-0 z-50 -tranlate-y-1/2 top-1/2" />

      <SidebarContent>
        <SidebarGroup className="overflow-y-scroll">
          <SidebarGroupLabel className="py-6 2xl:py-8 px-0 grid place-content-center font-light text-lg">Klout Mapping Application</SidebarGroupLabel>
          <SidebarGroupContent className="mt-10">
            <SidebarMenu>

              {/* Home Link */}
              {(() => {
                const HomeIcon = regularLinks[0].icon;
                return (
                  <Link
                    to={regularLinks[0].url}
                    title={!isOpen ? regularLinks[0].title : ""}
                    className={`flex items-center px-2 py-4 rounded hover:bg-black/20 ${location.pathname === regularLinks[0].url ? "bg-black/20" : ""
                      }`}
                  >
                    <HomeIcon size={24} className="w-16" />
                    {isOpen && <span className="font-medium">{regularLinks[0].title}</span>}
                  </Link>
                );
              })()}

              {/* Accordion Groups */}
              {isOpen ? (
                // When sidebar is expanded - show accordion
                <Accordion type="multiple" className="w-full">
                  {dropdownGroups.map((group) => {
                    const GroupIcon = group.icon;
                    return (
                      <AccordionItem key={group.title} value={group.title} className="border-none">
                        <AccordionTrigger
                          isOpen={isOpen}
                          setIsOpen={setIsOpen}
                          className={`flex items-center justify-between w-full px-2 py-4 rounded hover:bg-black/20 hover:no-underline ${isActiveInGroup(group.items) ? "bg-black/20" : ""
                            }`}
                        >
                          <div className="flex items-center">
                            <GroupIcon size={24} className="w-16" />
                            <span className="font-medium">{group.title}</span>
                          </div>
                        </AccordionTrigger>
                        <AccordionContent className="pb-0">
                          <div className="ml-8 space-y-1">
                            {group.items.map((item) => {
                              const ItemIcon = item.icon;
                              return (
                                <Link
                                  key={item.url}
                                  to={item.url}
                                  className={`flex items-center px-3 py-2 text-sm rounded hover:bg-black/20 ${location.pathname === item.url ? "bg-black/20" : ""
                                    }`}
                                >
                                  <ItemIcon size={16} className="mr-2" />
                                  {item.title}
                                </Link>
                              );
                            })}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    );
                  })}
                </Accordion>
              ) : (
                // When sidebar is collapsed - show individual icons with tooltips
                dropdownGroups.map((group) => {
                  const GroupIcon = group.icon;
                  return (
                    <div key={group.title} className="relative group">
                      <button
                        className={`flex items-center justify-center w-full px-2 py-4 rounded hover:bg-black/20 ${isActiveInGroup(group.items) ? "bg-black/20" : ""
                          }`}
                        title={group.title}
                      >
                        <GroupIcon size={24} className="w-16" />
                      </button>
                      {/* Tooltip on hover for collapsed state */}
                      <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 bg-brandPrimary border border-gray-600 text-white px-2 py-1 rounded text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-nowrap">
                        {group.title}
                      </div>
                    </div>
                  );
                })
              )}

              {/* Regular Links (excluding Home which is already rendered) */}
              {regularLinks.slice(1).map((item) => {
                const ItemIcon = item.icon;
                return (
                  <Link
                    key={item.url}
                    to={item.url}
                    title={!isOpen ? item.title : ""}
                    className={`flex items-center px-2 py-4 rounded hover:bg-black/20 ${location.pathname === item.url ? "bg-black/20" : ""
                      }`}
                  >
                    <ItemIcon size={24} className="w-16" />
                    {isOpen && <span className="font-medium">{item.title}</span>}
                  </Link>
                );
              })}

            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
