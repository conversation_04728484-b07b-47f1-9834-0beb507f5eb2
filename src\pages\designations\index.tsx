import Spinner from '@/components/Spinner';
import { domain2, token } from '@/lib/utils';
import axios from 'axios';
import React, { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Edit, Plus } from 'lucide-react';

import { Designation } from '@/types';
import { SquarePen, Trash } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
const Designations: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [itemsPerPage] = useState<number>(50);
  const [query, setQuery] = useState<string>("");
  const [designations, setDesignations] = useState<Designation[]>([]);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [designation, setDesignation] = useState<string>("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState<boolean>(false);
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState<boolean>(false);
  const [selectedDesignation, setSelectedDesignation] = useState<Designation | null>(null);

  useEffect(() => {
    setLoading(true);
    axios.get(`${domain2}/api/mapping/v1/designation-master/all-designation?page=${pageNumber}&search=${query}`, {
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      }
    }).then(res => {
      setDesignations(res.data.data.designations);
      setTotalPages(Math.ceil(res.data.data.totalDesignations / itemsPerPage));
    }).catch(err => {
      console.error(err);
      toast({
        title: "Failed to fetch designations",
        variant: "destructive",
      });
    }).finally(() => setLoading(false));
  }, [query, pageNumber]);

  const addDesignation = async (designation: string) => {
    try {
      setLoading(true);
      const res = await axios.post(`${domain2}/api/mapping/v1/designation-master/add-designation`, {
        designation, mappedTo: []
      }, {
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });

      if (res.data.status) {
        toast({
          title: res.data.message,
          description: "Designation added successfully",
          variant: "default",
          className: "bg-green-800 text-white"
        });
        setDesignation("");
        setIsAddDialogOpen(false);
        // Refresh data
        setQuery("");
        setPageNumber(1);
      } else {
        toast({
          title: res.data.message,
          variant: "destructive",
        });
      }
    } catch (err) {
      let errorMessage = "Something went wrong";
      if (axios.isAxiosError(err) && err.response) {
        errorMessage = err.response.data.message || err.message;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      toast({
        title: errorMessage,
        description: "Failed to add designation",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }

  const deleteDesignation = async (id: string) => {
    try {
      setLoading(true);
      const res = await axios.delete(`${domain2}/api/mapping/v1/designation-master/delete-designation/${id}`, {
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      });
      if (res.data.status) {
        toast({
          title: res.data.message,
          description: "Designation deleted successfully",
          variant: "default",
          className: "bg-green-800 text-white"
        });
        // Refresh data
        setQuery("");
        setPageNumber(1);
      } else {
        toast({
          title: res.data.message,
          variant: "destructive",
        });
      }
    } catch (err) {
      toast({
        title: "Failed to delete designation",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }

  const updateDesignation = async (designation: string, id: string) => {
    try {
      setLoading(true);
      const response = await axios.patch(`${domain2}/api/mapping/v1/designation-master/update-designation/${id}`, {
        designation, mappedTo: []
      });
      if (response.data.status) {
        toast({
          title: response.data.message,
          description: "Designation updated successfully",
          variant: "default",
          className: "bg-green-800 text-white"
        });
        setIsUpdateDialogOpen(false);
        setSelectedDesignation(null);
        // Refresh data
        setQuery("");
        setPageNumber(1);
      } else {
        toast({
          title: response.data.message,
          variant: "destructive",
        });
      }
    } catch (err) {
      toast({
        title: "Failed to update designation",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }

  const handleSendBack = async (designation: string) => {
    try {
      setLoading(true);
      const response = await axios.post(`${domain2}/api/mapping/v1/designation-master/send-back-new-designation-to-unmpapped-or-new-designation`, {
        designation
      });

      if (response.data.status) {
        setQuery("");
        toast({
          title: response.data.message,
          variant: "default",
          className: "bg-green-800 text-white"
        });
      } else {
        toast({
          title: response.data.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Failed to send back designation",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className='w-full'>
      <div className='flex justify-between flex-wrap-reverse gap-3 mt-5 w-full mb-3'>
        <Input
          type='text'
          className='max-w-60'
          onChange={(e) => setQuery(e.target.value)}
          placeholder='Filter by designation'
          value={query}
        />
        <div className='flex gap-3'>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className='bg-brandPrimary hover:bg-brandPrimaryDark text-white'>Add Designation<Plus /></Button>
            </DialogTrigger>
            <DialogContent className='max-w-96'>
              <DialogTitle className='text-2xl text-center mb-5'>Add Designation</DialogTitle>
              <Input
                type='text'
                value={designation}
                onChange={(e) => setDesignation(e.target.value)}
                placeholder="Enter designation name"
              />
              <Button
                onClick={() => addDesignation(designation)}
                className='bg-brandPrimary hover:bg-brandPrimaryDark text-white'
              >
                Add Designation <Plus />
              </Button>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div>
        {loading ? (
          <div className="flex justify-center items-center h-48">
            <Spinner />
          </div>
        ) : (
          <Table className='text-base'>
            <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
              <TableRow>
                <TableHead className='text-white'>Id</TableHead>
                <TableHead className='text-white'>Designation</TableHead>
                <TableHead className='text-white'>Mapped Designations</TableHead>
                <TableHead className='text-white text-right'>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {designations?.map((data: Designation, index: number) => (
                <TableRow key={data._id}>
                  <TableCell className='capitalize'>{(pageNumber - 1) * itemsPerPage + index + 1}</TableCell>
                  <TableCell className='capitalize'>{data.designation}</TableCell>
                  <TableCell className='capitalize max-w-96 text-wrap'>
                    {data.mappedTo && data.mappedTo.length > 0 
                      ? data.mappedTo.join(', ')
                      : ''}
                  </TableCell>
                  <TableCell className='capitalize'>
                    <div className='flex gap-2 justify-end items-center'>
                      <Dialog
                        open={isUpdateDialogOpen && selectedDesignation?._id === data._id}
                        onOpenChange={(open) => {
                          if (!open) {
                            setIsUpdateDialogOpen(false);
                            setSelectedDesignation(null);
                          } else {
                            setSelectedDesignation(data);
                            setIsUpdateDialogOpen(true);
                          }
                        }}
                      >
                        <DialogTrigger asChild>
                          <Button
                            variant={"ghost"}
                            className="p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-cyan-600/50 bg-cyan-500"
                            onClick={() => {
                              setSelectedDesignation(data);
                              setIsUpdateDialogOpen(true);
                            }}
                          >
                            <SquarePen />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className='max-w-96'>
                          <DialogDescription></DialogDescription>
                          <DialogTitle className='text-2xl text-center mb-5'>Update Designation</DialogTitle>
                          <Input type='text' defaultValue={data.designation} onChange={(e) => setDesignation(e.target.value)} placeholder="Enter your designation name" />
                          <Button onClick={() => updateDesignation(designation, data._id)} className='bg-brandPrimary hover:bg-brandPrimaryDark text-white'>Update Designation <Edit /></Button>
                        </DialogContent>
                      </Dialog>

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            className={`p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-red-600/50 bg-red-500`}
                          >
                            <Trash />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              Do you want to delete {data.designation} designation?
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => deleteDesignation(data._id)} className='bg-red-800 hover:bg-red-900 text-white duration-300'>Delete</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>


                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button className='bg-brandPrimary hover:bg-brandPrimaryDark text-white'>
                            Send Back
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              Do you want to send back {data.designation} designation?
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => handleSendBack(data.designation)} className='bg-brandPrimary hover:bg-brandPrimaryDark text-white duration-300'>Send Back</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>

                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>

      <div className='mt-10'>
        <Pagination>
          <PaginationContent>
            <PaginationItem className="cursor-pointer">
              <PaginationPrevious
                onClick={() => setPageNumber((prev) => Math.max(1, prev - 1))}
                className={pageNumber === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === 1}
              >
                Prev
              </PaginationPrevious>
            </PaginationItem>

            <PaginationItem className="cursor-pointer">
              <PaginationLink
                onClick={() => setPageNumber(1)}
                isActive={pageNumber === 1}
              >
                1
              </PaginationLink>
            </PaginationItem>

            {pageNumber > 3 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {Array.from({ length: 3 }, (_, index) => {
              const page = pageNumber - 1 + index;
              if (page > 1 && page < totalPages) {
                return (
                  <PaginationItem className="cursor-pointer" key={page}>
                    <PaginationLink
                      isActive={pageNumber === page}
                      onClick={() => setPageNumber(page)}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              }
              return null;
            })}

            {pageNumber < totalPages - 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {totalPages > 1 && (
              <PaginationItem className="cursor-pointer">
                <PaginationLink
                  onClick={() => setPageNumber(totalPages)}
                  isActive={pageNumber === totalPages}
                >
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            )}

            <PaginationItem className="cursor-pointer">
              <PaginationNext
                onClick={() => setPageNumber((prev) => Math.min(totalPages, prev + 1))}
                className={pageNumber === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === totalPages}
              >
                Next
              </PaginationNext>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
}

export default Designations;
