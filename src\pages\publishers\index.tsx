import { domain2 } from '@/lib/utils';
import { Publisher } from '@/types';
import axios from 'axios';
import React, { useEffect, useState } from 'react';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Button } from '@/components/ui/button';
import { Plus, SquarePen, Trash } from 'lucide-react';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/hooks/use-toast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogFooter, AlertDialogDescription, AlertDialogTitle, AlertDialogHeader, AlertDialogContent, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import Spinner from '@/components/Spinner';
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

const Publishers: React.FC = () => {
    const [publishers, setPublishers] = useState<Publisher[]>([]);
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        url: '',
        da: '',
        visits: ''
    });
    const [editFormData, setEditFormData] = useState({
        id: '',
        name: '',
        url: '',
        da: '',
        visits: ''
    });
    const [errors, setErrors] = useState({
        name: '',
        url: '',
        da: '',
        visits: ''
    });
    const [dialogOpen, setDialogOpen] = useState(false);
    const [editDialogOpen, setEditDialogOpen] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemPerPage] = useState(10);
    const [publisherFilter, setPublisherFilter] = useState('');

    const getAllPublishers = async () => {
        try {
            setLoading(true);
            axios.get(`${domain2}/api/v1/tls/get-all-news-publishers`).then((res) => {
                setPublishers(res.data.data);
                console.log(res.data.data);
            }).catch((err) => {
                console.log(err);
            })
        } catch (err) {
            console.log(err);
        } finally {
            setLoading(false);
        }
    }
    useEffect(() => {
        getAllPublishers();
    }, []);

    const validateForm = () => {
        let isValid = true;
        const newErrors = { name: '', url: '', da: '', visits: '' };

        if (!formData.name.trim()) {
            newErrors.name = 'Name is required';
            isValid = false;
        }

        if (!formData.url.trim()) {
            newErrors.url = 'URL is required';
            isValid = false;
        } else if (!/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/.test(formData.url)) {
            newErrors.url = 'Invalid URL format';
            isValid = false;
        }

        if (!formData.da.trim()) {
            newErrors.da = 'DA is required';
            isValid = false;
        } else if (isNaN(Number(formData.da))) {
            newErrors.da = 'DA must be a number';
            isValid = false;
        }

        if (!formData.visits.trim()) {
            newErrors.visits = 'Visits is required';
            isValid = false;
        } else if (isNaN(Number(formData.visits))) {
            newErrors.visits = 'Visits must be a number';
            isValid = false;
        }

        setErrors(newErrors);
        return isValid;
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setFormData({
            ...formData,
            [e.target.id]: e.target.value
        });
        // Clear error when user starts typing
        setErrors({
            ...errors,
            [e.target.id]: ''
        });
    };

    const handleSubmit = async () => {
        if (!validateForm()) return;

        try {
            const response = await axios.post(`${domain2}/api/v1/tls/add-news-publisher`, formData, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (response.data.status && response.data.data) {
                setDialogOpen(false); // Close dialog on success
                setPublishers([...publishers, response.data.data]);
                setFormData({
                    name: '',
                    url: '',
                    da: '',
                    visits: ''
                });
                toast({
                    title: response.data.message,
                    variant: "default",
                    className: "bg-green-800 text-white"
                });
            } else {
                toast({
                    title: response.data.message,
                    variant: "destructive"
                });
            }
        } catch (error: any) {
            console.error('Error adding publisher:', error);
            toast({
                title: error.response.data.message || "Error adding publisher",
                variant: "destructive"
            });
        }
    };

    const handleDialogOpenChange = (open: boolean) => {
        setDialogOpen(open);
        if (!open) {
            // Reset errors when dialog closes
            setErrors({
                name: '',
                url: '',
                da: '',
                visits: ''
            });
        }
    };

    const handleUpdate = async () => {
        console.log("Updated Data:", editFormData);
        // You can also add your logic to update the publisher in the backend here
        try {
            setLoading(true);
            const response = await axios.patch(`${domain2}/api/v1/tls/update-news-publisher/${editFormData.id}`, editFormData, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.data.status) {
                getAllPublishers();
                setEditDialogOpen(false);
                toast({
                    title: response.data.message,
                    variant: "default",
                    className: "bg-green-800 text-white"
                });
            } else {
                toast({
                    title: response.data.message,
                    variant: "destructive"
                });
            }
        } catch (error: any) {
            toast({
                title: error.response.data.message || "Error updating publisher",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    };

    const handleDeletePublisher = async (id: string) => {
        try {
            setLoading(true);
            const response = await axios.delete(`${domain2}/api/v1/tls/delete-news-publisher/${id}`);
            if (response.data.status) {
                getAllPublishers();
                toast({
                    title: response.data.message,
                    variant: "default",
                    className: "bg-green-800 text-white"
                });
            } else {
                toast({
                    title: response.data.message,
                    variant: "destructive"
                });
            }
        } catch (error: any) {
            toast({
                title: error.response.data.message || "Error deleting publisher",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    }

    // Filter publishers based on search term
    const filteredPublishers = publishers.filter(publisher => 
        publisher.name.toLowerCase().includes(publisherFilter.toLowerCase()) ||
        publisher.url.toLowerCase().includes(publisherFilter.toLowerCase())
    );

    // Calculate pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentPublishers = filteredPublishers.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredPublishers.length / itemsPerPage);

    if (loading) {
        return (
            <div className='h-full w-full grid place-content-center'>
                <Spinner />
            </div>
        )
    }

    return (
        <div>
            <div className='flex justify-between items-center mt-5 w-full mb-3'>
                <div className="flex gap-3">
                    <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemPerPage(Number(value))}>
                        <SelectTrigger className="w-fit">
                            <SelectValue placeholder="Show Rows" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectLabel>Select Rows</SelectLabel>
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="25">25</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>

                    <Input
                        type="text"
                        className="max-w-60"
                        onChange={(e) => setPublisherFilter(e.target.value)}
                        placeholder="Filter by name or URL"
                    />
                </div>
                <Dialog open={dialogOpen} onOpenChange={handleDialogOpenChange}>
                    <DialogTrigger asChild>
                        <Button className='bg-brandPrimary hover:bg-brandPrimaryDark duration-300 text-white'>
                            Add Publisher <Plus />
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Add New Publisher</DialogTitle>
                            <DialogDescription>
                                Enter the details of the new publisher
                            </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="name">Name</Label>
                                <Input id="name" value={formData.name} onChange={handleChange} />
                                {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
                            </div>
                            <div>
                                <Label htmlFor="url">URL</Label>
                                <Input id="url" value={formData.url} onChange={handleChange} />
                                {errors.url && <p className="text-red-500 text-sm">{errors.url}</p>}
                            </div>
                            <div>
                                <Label htmlFor="da">DA</Label>
                                <Input id="da" value={formData.da} onChange={handleChange} />
                                {errors.da && <p className="text-red-500 text-sm">{errors.da}</p>}
                            </div>
                            <div>
                                <Label htmlFor="visits">Visits</Label>
                                <Input id="visits" value={formData.visits} onChange={handleChange} />
                                {errors.visits && <p className="text-red-500 text-sm">{errors.visits}</p>}
                            </div>
                            <Button onClick={handleSubmit} className="w-full text-white bg-brandPrimary hover:bg-brandPrimaryDark">
                                Add Publisher
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog>
            </div>
            <div className='mt-3'>
                <Table className='text-base text-nowrap'>
                    <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
                        <TableRow>
                            <TableHead className='text-white'>S.No</TableHead>
                            <TableHead className='text-white'>Name</TableHead>
                            <TableHead className='text-white'>URL</TableHead>
                            <TableHead className='text-white'>DA</TableHead>
                            <TableHead className='text-white'>Visits</TableHead>
                            <TableHead className='text-white text-right'>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {currentPublishers.map((publisher, index) => (
                            <TableRow key={publisher._id}>
                                <TableCell>{indexOfFirstItem + index + 1}</TableCell>
                                <TableCell>{publisher.name}</TableCell>
                                <TableCell>{publisher.url}</TableCell>
                                <TableCell>{publisher.da}</TableCell>
                                <TableCell>{publisher.visits}</TableCell>
                                <TableCell className='text-right flex gap-2 justify-end'>

                                    {/* Edit Button */}
                                    <Dialog open={editDialogOpen && publisher._id === editFormData.id}
                                        onOpenChange={(open) => {
                                            if (!open) {
                                                setEditDialogOpen(false);
                                                setEditFormData({
                                                    id: '',
                                                    name: '',
                                                    url: '',
                                                    da: '',
                                                    visits: ''
                                                });
                                            } else {
                                                setEditDialogOpen(true);
                                                setEditFormData({
                                                    id: publisher._id,
                                                    name: publisher.name,
                                                    url: publisher.url,
                                                    da: publisher.da.toString(),
                                                    visits: publisher.visits
                                                });
                                            }
                                        }}>
                                        <DialogTrigger asChild>
                                            <Button
                                                variant={"ghost"}
                                                className="p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-cyan-600/50 bg-cyan-500"
                                                onClick={() => setEditFormData({
                                                    id: publisher._id,
                                                    name: publisher.name,
                                                    url: publisher.url,
                                                    da: publisher.da.toString(),
                                                    visits: publisher.visits
                                                })}
                                            >
                                                <SquarePen />
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent>
                                            <DialogHeader>
                                                <DialogTitle>Update Publisher</DialogTitle>
                                                <DialogDescription>
                                                    Update the details of the new publisher
                                                </DialogDescription>
                                            </DialogHeader>
                                            <div className="space-y-4">
                                                <div>
                                                    <Label htmlFor="updateName">Name</Label>
                                                    <Input
                                                        id="updateName"
                                                        value={editFormData.name}
                                                        onChange={(e) => setEditFormData({ ...editFormData, name: e.target.value })}
                                                    />
                                                </div>
                                                <div>
                                                    <Label htmlFor="updateUrl">URL</Label>
                                                    <Input
                                                        id="updateUrl"
                                                        value={editFormData.url}
                                                        onChange={(e) => setEditFormData({ ...editFormData, url: e.target.value })}
                                                    />
                                                </div>
                                                <div>
                                                    <Label htmlFor="updateDa">DA</Label>
                                                    <Input
                                                        id="updateDa"
                                                        value={editFormData.da}
                                                        onChange={(e) => setEditFormData({ ...editFormData, da: e.target.value })}
                                                    />
                                                </div>
                                                <div>
                                                    <Label htmlFor="updateVisits">Visits</Label>
                                                    <Input
                                                        id="updateVisits"
                                                        value={editFormData.visits}
                                                        onChange={(e) => setEditFormData({ ...editFormData, visits: e.target.value })}
                                                    />
                                                </div>
                                                <Button
                                                    onClick={() => handleUpdate()}
                                                    className="w-full text-white bg-brandPrimary hover:bg-brandPrimaryDark"
                                                >
                                                    Update Publisher
                                                </Button>
                                            </div>
                                        </DialogContent>
                                    </Dialog>


                                    {/* Delete Button */}
                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                className={`p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-red-600/50 bg-red-500`}
                                            >
                                                <Trash />
                                            </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    This will permanently delete the {publisher.name} publisher.
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction
                                                    className="bg-red-500 hover:bg-red-600 text-white"
                                                    onClick={() => handleDeletePublisher(publisher._id)}
                                                >
                                                    Delete
                                                </AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
                
                {totalPages > 1 && (
                    <div className="mt-4 flex justify-center">
                        <Pagination>
                            <PaginationContent>
                                <PaginationItem>
                                    <PaginationPrevious 
                                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                        className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                                    />
                                </PaginationItem>
                                
                                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => {
                                    // Show first page, last page, and pages around current page
                                    if (
                                        page === 1 || 
                                        page === totalPages || 
                                        (page >= currentPage - 1 && page <= currentPage + 1)
                                    ) {
                                        return (
                                            <PaginationItem key={page}>
                                                <PaginationLink 
                                                    onClick={() => setCurrentPage(page)}
                                                    isActive={currentPage === page}
                                                >
                                                    {page}
                                                </PaginationLink>
                                            </PaginationItem>
                                        );
                                    }
                                    
                                    // Show ellipsis for gaps
                                    if (
                                        (page === 2 && currentPage > 3) || 
                                        (page === totalPages - 1 && currentPage < totalPages - 2)
                                    ) {
                                        return (
                                            <PaginationItem key={page}>
                                                <PaginationEllipsis />
                                            </PaginationItem>
                                        );
                                    }
                                    
                                    return null;
                                })}
                                
                                <PaginationItem>
                                    <PaginationNext 
                                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                        className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                )}
            </div>
        </div>
    )
}

export default Publishers;
