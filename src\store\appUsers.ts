import { AppUser } from "@/types";
import { create } from "zustand";
import { persist, PersistOptions } from "zustand/middleware";

// Define the types
interface AppUserStore {
    allAppUsers: AppUser[];
    setAllAppUsers: (customers: AppUser[]) => void;
}

// Create the store
const useAppUserStore = create<AppUserStore>()(
    persist(
        (set) => ({
            allAppUsers: [],
            setAllAppUsers: (appUser: AppUser[]) => set(() => ({
                allAppUsers: appUser
            })),
        }),
        {
            name: "appUsers-storage", // Unique name for persistence
        } as PersistOptions<AppUserStore>
    )
);

export default useAppUserStore;
