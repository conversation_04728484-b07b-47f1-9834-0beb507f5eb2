import { Company } from "@/types";
import { create } from "zustand";
import { persist, PersistOptions } from "zustand/middleware";

// Define the types
interface CompanyStore {
    companies: Company[];
    totalCompanies: number;
    addCompany: (company: Company) => void;
    setAllCompanies: (company: Company[], total: number) => void;
    removeCompany: (id: string) => void;
    getAllCompanies: () => Company[];
}

// Create the store
const useCompanyStore = create<CompanyStore>()(
    persist(
        (set) => ({
            companies: [],
            totalCompanies: 0,
            addCompany: (company: Company) => set((state) => ({
                companies: [...state.companies, company],
            })),
            removeCompany: (id: string) => set((state) => ({
                companies: state.companies.filter(company => company._id !== id),
            })),
            setAllCompanies: (allCompanies: Company[], total: number) => set(() => ({
                companies: allCompanies,
                totalCompanies: total, // Update the total number of companies
            })),
            getAllCompanies: (): Company[] => {
                const state = useCompanyStore.getState(); // Access the current state directly
                return state.companies;
            },
        }),
        {
            name: "company-storage", // Unique name for persistence
        } as PersistOptions<CompanyStore>
    )
);

export default useCompanyStore;
