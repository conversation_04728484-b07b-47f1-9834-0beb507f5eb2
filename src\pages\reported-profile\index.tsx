import React, { useEffect, useState } from 'react';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Spinner from '@/components/Spinner';
import axios from 'axios';
import { domain } from '@/lib/utils';
import useBlockRecords from '@/store/blockRecordStore';
import DialogBox from '@/components/DialogBox';

const ReportedProfile: React.FC = () => {

  const [loading, setLoading] = useState<boolean>(false);

  const setConcernProfiles = useBlockRecords((state) => state.setConcernProfiles);
  const setBlockedUsers = useBlockRecords((state) => state.setBlockedUsers);
  const setReportedProfiles = useBlockRecords((state) => state.setReportedProfiles);

  const blockedUsers = useBlockRecords((state) => state.reportedProfiles);


  useEffect(() => {
    setLoading(true);
    axios.get(`${domain}/mapping/api/all-fraud-users-list`)
      .then(res => {
        setReportedProfiles(res.data.reportedProfile)
        setConcernProfiles(res.data.concernProfile);
        setBlockedUsers(res.data.data);
      }).finally(() => setLoading(false));
  }, []);

  if (loading) {
    return <Spinner />
  }

  return (
    <div className='w-full'>
      <div>
        <Table className='text-base w-full'>
          <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
            <TableRow>
              <TableHead className='text-white'>User Id</TableHead>
              <TableHead className='text-white'>Reason</TableHead>
              <TableHead className='text-white'>Status</TableHead>
              {/* <TableHead className="text-white !w-fit">Mapped With</TableHead> */}
              <TableHead className="text-white text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {blockedUsers?.map((data) => (
              <TableRow key={data.id}>
                <TableCell>{data.id}</TableCell>
                <TableCell>{data.reason}</TableCell>
                <TableCell>{data.status}</TableCell>
                <TableCell className="flex justify-end gap-2">
                  {/* <EditDialogBox type='company' onSubmit={() => { }} data={data} title='Edit Company' /> */}
                  <DialogBox trigger='block' description='This user will be blocked.' title={`Block User ${data.id}`} buttonText='Block' className='bg-yellow-500 hover:bg-yellow-600' onSubmit={()=>{}}/>
                  <DialogBox trigger='delete' description='This user will be deleted forever' title={`Delete User ${data.id}`} buttonText='Continue' onSubmit={()=>{}}/>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

export default ReportedProfile;