import React, { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import { MapInstituteType } from '@/types';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";

import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";

import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import Spinner from '@/components/Spinner'; // Import Spinner component for loading indicator
import axios from 'axios';
import { domain2 } from '@/lib/utils';
import { But<PERSON> } from '@/components/ui/button';
import { DialogTrigger } from '@/components/ui/dialog';
import { Dialog, DialogTitle, DialogContent } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Plus, Trash } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

// const domain2: String = "https://quantamcoder.space";

const UnmappedOrNewInstitute: React.FC = () => {
    const [loading, setLoading] = useState<boolean>(false);
    const [intialAllInstitutes, setIntialAllInstitutes] = useState<number>(0);
    const [mapInstituteData, setMapInstituteData] = useState<MapInstituteType[] | undefined>(undefined);
    const [filteredInstituteData, setFilteredInstituteData] = useState<MapInstituteType[] | undefined>(undefined);
    const [pageNumber, setPageNumber] = useState<number>(1); // Start with page 1
    const [totalPages, setTotalPages] = useState<number>(Math.ceil(intialAllInstitutes / 50)); // Default page calculation
    const [itemsPerPage, setItemPerPage] = useState<number>(10);
    const [instituteFilter, setInstituteFilter] = useState<string>("");
    const [instituteVariation, setInstituteVariation] = useState<string>("");

    const pageSize = 50; // Companies per page

    const fetchMapInstitutes = async () => {
        setLoading(true);
        try {
            const res = await axios.post(`${domain2}/api/mapping/v1/education-master/get-all-unmapped-or-new-institute`);

            const fetchedInstitutes = res.data.data;
            setMapInstituteData(fetchedInstitutes);
            setFilteredInstituteData(fetchedInstitutes); // Set the fetched data to be initially displayed
            setTotalPages(Math.ceil(fetchedInstitutes.length / pageSize));
            setIntialAllInstitutes(fetchedInstitutes.length);
        } catch (error) {
            console.error("Error fetching institutes:", error);
        } finally {
            setLoading(false);
        }
    }

    const deleteUnmappedOrNewInstitute = async (id: string) => {
        setLoading(true);
        try {
            const response = await axios.delete(`${domain2}/api/mapping/v1/education-master/delete-unmapped-or-new-institute/${id}`);
            if (response.data.status) {
                toast({
                    title: response.data.message || "Institute deleted successfully",
                    variant: "default",
                    className: "bg-green-800 text-white"
                });
            } else {
                toast({
                    title: response.data.message,
                    variant: "destructive"
                });
            }
            fetchMapInstitutes();
        } catch (error) {
            console.error("Error deleting institute:", error);
        } finally {
            setLoading(false);
        }
    }

    const handleAddToUnmapped = async (instituteName: string) => {
        const response = await axios.post(`${domain2}/api/mapping/v1/education-master/add-unmapped-institute`, {
            "institute": instituteName,
            "isMapped": false,
            "mappedWith": ""
        }, {
            headers: {
                "Content-Type": "application/json"
            }
        });

        if (response.data.status) {
            toast({
                title: "Institute Added to Unmapped",
                className: "bg-green-700 text-white"
            });
            fetchMapInstitutes();
        } else {
            toast({
                title: response.data.message,
                variant: "destructive"
            });
        }
    }

    const handleAddVariation = async () => {
        const response = await axios.post(`${domain2}/api/mapping/v1/education-master/add-unmapped-or-new-institute`, {
            "institute": instituteVariation,
            "isMapped": false,
            "mappedWith": ""
        }, {
            headers: {
                "Content-Type": "application/json"
            }
        });

        if (response.data.status) {
            toast({
                title: response.data.message || "A new variation added",
                className: "bg-green-700 text-white"
            });
            fetchMapInstitutes();
        } else {
            toast({
                title: response.data.message,
                variant: "destructive"
            });
        }
    }

    useEffect(() => {
        fetchMapInstitutes();
    }, []);

    useEffect(() => {
        if (instituteFilter) {
            const filteredData = mapInstituteData?.filter(institute =>
                institute.institute.toLowerCase().includes(instituteFilter.toLowerCase())
            );
            setFilteredInstituteData(filteredData);
            // Reset to first page when filtering
            setPageNumber(1);
            // Update total pages based on filtered data
            setTotalPages(Math.ceil((filteredData?.length || 0) / itemsPerPage));
        } else {
            setFilteredInstituteData(mapInstituteData);
            // Update total pages based on all data
            setTotalPages(Math.ceil((mapInstituteData?.length || 0) / itemsPerPage));
        }
    }, [instituteFilter, mapInstituteData, itemsPerPage]);

    // Update pagination when items per page changes
    useEffect(() => {
        if (filteredInstituteData) {
            setTotalPages(Math.ceil(filteredInstituteData.length / itemsPerPage));
            // If current page is now beyond total pages, reset to page 1
            if (pageNumber > Math.ceil(filteredInstituteData.length / itemsPerPage)) {
                setPageNumber(1);
            }
        }
    }, [itemsPerPage, filteredInstituteData]);


    if (loading) {
        return <Spinner />
    }

    // Generate page numbers to display
    const getPageNumbers = () => {
        const pageNumbers = [];

        // Always show first page
        pageNumbers.push(1);

        // Calculate range around current page
        let startPage = Math.max(2, pageNumber - 1);
        let endPage = Math.min(totalPages - 1, pageNumber + 1);

        // Add ellipsis indicator if needed
        if (startPage > 2) {
            pageNumbers.push('ellipsis-start');
        }

        // Add pages around current page
        for (let i = startPage; i <= endPage; i++) {
            pageNumbers.push(i);
        }

        // Add ellipsis indicator if needed
        if (endPage < totalPages - 1) {
            pageNumbers.push('ellipsis-end');
        }

        // Add last page if there's more than one page
        if (totalPages > 1) {
            pageNumbers.push(totalPages);
        }

        return pageNumbers;
    };

    return (
        <div className='w-full'>
            <div className='flex justify-between w-full items-center mt-5 mb-3'>
                <div className="flex flex-wrap-reverse gap-3 w-full">
                    <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemPerPage(Number(value))}>
                        <SelectTrigger className="w-fit">
                            <SelectValue placeholder="Show Rows" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectLabel>Select Rows</SelectLabel>
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="25">25</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>

                    <Input
                        type="text"
                        className="max-w-60"
                        onChange={(e) => setInstituteFilter(e.target.value)}
                        placeholder="Filter by institute name"
                    />
                </div>

                <Dialog>
                    <DialogTrigger asChild>
                        <Button className="bg-brandPrimary hover:bg-brandPrimaryDark text-white">
                            Add Variation <Plus />
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-sm">
                        <DialogTitle className="text-2xl text-center mb-5">
                            Add Institute Variation
                        </DialogTitle>
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="institute" className="text-sm font-normal">
                                    Institute Name <span className="text-destructive">*</span>
                                </Label>
                                <Input
                                    id="institute"
                                    type="text"
                                    className='mt-2'
                                    onChange={(e) => setInstituteVariation(e.target.value)}
                                    placeholder="Enter institute name"
                                />
                                <Button onClick={() => handleAddVariation()} className='bg-brandPrimary hover:bg-brandPrimaryDark text-white w-full mt-5'>Add Variation</Button>
                            </div>
                        </div>
                    </DialogContent>
                </Dialog>
            </div>

            <div>
                <Table className='text-base'>
                    <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
                        <TableRow>
                            <TableHead className='text-white'>S.No</TableHead>
                            <TableHead className='text-white'>Institute Name</TableHead>
                            <TableHead className="text-white text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredInstituteData?.slice((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage).map((data, index) => (
                            <TableRow key={data._id}>
                                <TableCell>{(pageNumber - 1) * itemsPerPage + index + 1}</TableCell>
                                <TableCell>{data.institute}</TableCell>
                                <TableCell className='text-right flex items-center justify-end gap-x-3'>

                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            {/* <Button className='px-3 py-1 text-sm bg-orange-600 hover:bg-orange-700 text-white'>Add To Unmapped</Button> */}
                                            <Button className='px-3 py-1 text-sm bg-green-700 hover:bg-green-800 text-white'>Add To New</Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    Do you really want to add {data.institute} into unmapped database ?
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={()=>{}} className='text-sm bg-green-700 hover:bg-green-800 px-3 py-1 text-white'>Continue</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>

                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button className='px-3 py-1 text-sm bg-orange-600 hover:bg-orange-700 text-white'>Add To Unmapped</Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    Do you really want to add {data.institute} into unmapped database ?
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleAddToUnmapped(data.institute)} className='text-sm bg-orange-600 hover:bg-orange-700 px-3 py-1 text-white'>Continue</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>

                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            {/* <Button className='px-3 py-1 text-sm bg-red-700 hover:bg-red-800 text-white'>Delete</Button> */}
                                            <Button
                                                variant={"ghost"}
                                                className="p-3 ml-2 h-5 w-5 rounded-full text-white hover:text-white hover:bg-red-600/50 bg-red-500"
                                            >
                                                <Trash />
                                            </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    Do you really want to delete {data.institute} from unmapped database ?
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => deleteUnmappedOrNewInstitute(data._id)} className='text-sm bg-red-700 hover:bg-red-800 px-3 py-1 text-white'>Continue</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>

                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            <div className='mt-10'>
                <Pagination>
                    <PaginationContent>
                        <PaginationItem className="cursor-pointer">
                            <PaginationPrevious
                                onClick={() => setPageNumber((prev) => Math.max(1, prev - 1))}
                                className={pageNumber === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                                aria-disabled={pageNumber === 1}
                            >
                                Prev
                            </PaginationPrevious>
                        </PaginationItem>

                        {getPageNumbers().map((page, index) => {
                            if (page === 'ellipsis-start' || page === 'ellipsis-end') {
                                return (
                                    <PaginationItem key={`ellipsis-${index}`}>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                );
                            }

                            return (
                                <PaginationItem className="cursor-pointer" key={page}>
                                    <PaginationLink
                                        isActive={pageNumber === page}
                                        onClick={() => setPageNumber(Number(page))}
                                    >
                                        {page}
                                    </PaginationLink>
                                </PaginationItem>
                            );
                        })}

                        <PaginationItem className="cursor-pointer">
                            <PaginationNext
                                onClick={() => setPageNumber((prev) => Math.min(totalPages, prev + 1))}
                                className={pageNumber === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                                aria-disabled={pageNumber === totalPages}
                            >
                                Next
                            </PaginationNext>
                        </PaginationItem>
                    </PaginationContent>
                </Pagination>
            </div>
        </div>
    );
}

export default UnmappedOrNewInstitute;