import React, { useEffect } from 'react';
import Spinner from '@/components/Spinner';
import { domain2, token } from '@/lib/utils';
import axios from 'axios';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Input } from "@/components/ui/input";
import { Button } from '@/components/ui/button';
import { Edit } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { toast } from '@/hooks/use-toast';
import { Label } from '@/components/ui/label';

interface UserSocialMedia {
  _id: string;
  first_name: string;
  last_name: string;
  company: string;
  designation: string;
  linkedinProfileUrl: string;
  xProfileUrl: string;
  linkedinFollowers: number;
  xFollowers: number;
}

const updateUserSocialMedia = async (id: string, linkedinFollowersCount: number, xFollowersCount: number) => {
  try {
    const response = await axios.patch(
      `${domain2}/api/mapping/v1/company-master/update-user-linkedin-or-x-follower-count/${id}`,
      {linkedinFollowersCount, xFollowersCount},
      {
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        }
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error updating user social media:', error);
    throw error;
  }
}

const getUserSocialMedia = async (page: number = 1) => {
  try {
    const response = await axios.post(`${domain2}/api/mapping/v1/company-master/view-all-user-with-linkedin-or-twitter?page=${page}`, {}, {
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      }
    });
    return response.data.data || response.data;
  } catch (error) {
    console.error('Error fetching user social media:', error);
    return { users: [], totalUsers: 0 };
  }
};

const UserSocialMedia = () => {
  const [allUserData, setAllUserData] = React.useState<UserSocialMedia[]>([]); // Store all unfiltered data
  const [filteredUserData, setFilteredUserData] = React.useState<UserSocialMedia[]>([]); // Store filtered data
  const [loading, setLoading] = React.useState(true);
  const [pageNumber, setPageNumber] = React.useState(1);
  const [totalPages, setTotalPages] = React.useState(1);
  const [itemsPerPage, setItemsPerPage] = React.useState(50);

  const [selectedUserId, setSelectedUserId] = React.useState<string | null>(null);
  const [editFollowers, setEditFollowers] = React.useState({
    linkedinFollowers: 0,
    xFollowers: 0
  });

  // Filter states
  const [nameFilter, setNameFilter] = React.useState('');
  const [companyFilter, setCompanyFilter] = React.useState('');
  const [designationFilter, setDesignationFilter] = React.useState('');
  const [linkedinFollowersFilter, setLinkedinFollowersFilter] = React.useState('all');
  const [xFollowersFilter, setXFollowersFilter] = React.useState('all');

  const handleUpdateFollowers = async () => {
    if (!selectedUserId) return;

    try {
      setLoading(true);
      const response = await updateUserSocialMedia(
        selectedUserId,
        editFollowers.linkedinFollowers,
        editFollowers.xFollowers
      );

      if (response.success) { // Changed from response.status to response.success
        toast({
          title: response.message, // Use the message from the response
          variant: "default",
          className: "bg-green-800 text-white"
        });
        
        // Update local data
        const updatedData = allUserData.map(user => {
          if (user._id === selectedUserId) {
            return {
              ...user,
              linkedinFollowers: editFollowers.linkedinFollowers,
              xFollowers: editFollowers.xFollowers
            };
          }
          return user;
        });
        
        setAllUserData(updatedData);
        setFilteredUserData(updatedData);
        setSelectedUserId(null);
      } else {
        toast({
          title: response.message || "Failed to update followers",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error updating followers",
        description: String(error),
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch data only once when component mounts
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const response = await getUserSocialMedia(pageNumber);
      const data = Array.isArray(response) ? response : response.users || [];
      setAllUserData(data);
      setFilteredUserData(data);
      setTotalPages(Math.ceil(data.length / itemsPerPage));
      setLoading(false);
    };

    fetchData();
  }, []); // Empty dependency array means this runs once on mount

  // Handle filtering separately
  useEffect(() => {
    const filtered = allUserData.filter(user => {
      const fullName = `${user.first_name} ${user.last_name}`.toLowerCase();
      const matchName = fullName.includes(nameFilter.toLowerCase());
      const matchCompany = user.company.toLowerCase().includes(companyFilter.toLowerCase());
      const matchDesignation = user.designation.toLowerCase().includes(designationFilter.toLowerCase());

      // LinkedIn followers filter
      const matchLinkedin = linkedinFollowersFilter === 'all' ? true :
        linkedinFollowersFilter === 'zero' ? user.linkedinFollowers === 0 :
          user.linkedinFollowers > 0;

      // X followers filter
      const matchX = xFollowersFilter === 'all' ? true :
        xFollowersFilter === 'zero' ? user.xFollowers === 0 :
          user.xFollowers > 0;

      return matchName && matchCompany && matchDesignation && matchLinkedin && matchX;
    });

    setFilteredUserData(filtered);
    setTotalPages(Math.ceil(filtered.length / itemsPerPage));
    setPageNumber(1); // Reset to first page when filters change
  }, [nameFilter, companyFilter, designationFilter, linkedinFollowersFilter, xFollowersFilter, itemsPerPage, allUserData]);

  // Get current page data
  const getCurrentPageData = () => {
    const startIndex = (pageNumber - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredUserData.slice(startIndex, endIndex);
  };

  if (loading) {
    return <Spinner />;
  }

  return (
    <div className="container mx-auto p-6">
      {/* Filters Bar */}
      <div className='flex gap-3 mb-3 overflow-x-scroll p-1'>
        {/* Select Rows */}
        <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
          <SelectTrigger className="w-fit">
            <SelectValue placeholder="Show Rows" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Select Rows</SelectLabel>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>

        {/* LinkedIn Followers Filter */}
        <Select value={linkedinFollowersFilter} onValueChange={setLinkedinFollowersFilter}>
          <SelectTrigger className="w-fit min-w-36">
            <SelectValue placeholder="LinkedIn Followers" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>LinkedIn Followers</SelectLabel>
              <SelectItem value="all">All Profiles</SelectItem>
              <SelectItem value="zero">0 Followers</SelectItem>
              <SelectItem value="nonzero">Has Followers</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>

        {/* X Followers Filter */}
        <Select value={xFollowersFilter} onValueChange={setXFollowersFilter}>
          <SelectTrigger className="w-fit min-w-36">
            <SelectValue placeholder="X Followers" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>X Followers</SelectLabel>
              <SelectItem value="all">All Profiles</SelectItem>
              <SelectItem value="zero">0 Followers</SelectItem>
              <SelectItem value="nonzero">Has Followers</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>

        {/* Filter By Name */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            value={nameFilter}
            onChange={(e) => setNameFilter(e.target.value)}
            placeholder="Filter by name"
          />
        </div>

        {/* Filter By Company */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            value={companyFilter}
            onChange={(e) => setCompanyFilter(e.target.value)}
            placeholder="Filter by company"
          />
        </div>

        {/* Filter By Designation */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            value={designationFilter}
            onChange={(e) => setDesignationFilter(e.target.value)}
            placeholder="Filter by designation"
          />
        </div>
      </div>

      <Table className='text-base mt-5'>
        <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
          <TableRow>
            <TableHead className='text-white'>S.No</TableHead>
            <TableHead className='text-white'>Actions</TableHead>
            <TableHead className='text-white'>Name</TableHead>
            <TableHead className='text-white'>LinkedIn Followers</TableHead>
            <TableHead className='text-white'>X Followers</TableHead>
            <TableHead className='text-white'>Company</TableHead>
            <TableHead className='text-white'>Designation</TableHead>
            <TableHead className='text-white'>LinkedIn Profile URL</TableHead>
            <TableHead className='text-white'>X Profile URL</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {getCurrentPageData().map((user, index) => (
            <TableRow key={user._id}>
              <TableCell>{((pageNumber - 1) * itemsPerPage) + index + 1}</TableCell>
              <TableCell className="text-center">
                <Dialog 
                  open={selectedUserId === user._id} 
                  onOpenChange={(open) => {
                    if (open) {
                      setSelectedUserId(user._id);
                      setEditFollowers({
                        linkedinFollowers: user.linkedinFollowers,
                        xFollowers: user.xFollowers
                      });
                    } else {
                      setSelectedUserId(null);
                    }
                  }}
                >
                  <DialogTrigger asChild>
                    <Button
                      variant="ghost"
                      className="p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-orange-600/50 bg-orange-500"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[425px]">
                    <DialogTitle className='capitalize'>{user.first_name} {user.last_name}</DialogTitle>
                    <DialogDescription>
                      Update the followers count for {user.first_name} {user.last_name}
                    </DialogDescription>
                    <div className="grid gap-4 py-4">
                      <div className="grid gap-2">
                        <Label htmlFor="linkedinFollowers">LinkedIn Followers</Label>
                        <Input
                          id="linkedinFollowers"
                          type="text"
                          value={editFollowers.linkedinFollowers}
                          onChange={(e) => setEditFollowers(prev => ({
                            ...prev,
                            linkedinFollowers: parseInt(e.target.value) || 0
                          }))}
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="xFollowers">X Followers</Label>
                        <Input
                          id="xFollowers"
                          type="text"
                          value={editFollowers.xFollowers}
                          onChange={(e) => setEditFollowers(prev => ({
                            ...prev,
                            xFollowers: parseInt(e.target.value) || 0
                          }))}
                        />
                      </div>
                      <Button
                        onClick={handleUpdateFollowers}
                        className="bg-brandPrimary hover:bg-brandPrimaryDark text-white"
                        disabled={loading}
                      >
                        {loading ? "Updating..." : "Update Followers"}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </TableCell>
              <TableCell>{`${user.first_name} ${user.last_name}`}</TableCell>
              <TableCell>{user.linkedinFollowers}</TableCell>
              <TableCell>{user.xFollowers}</TableCell>
              <TableCell>{user.company}</TableCell>
              <TableCell>{user.designation}</TableCell>
              <TableCell className='max-w-96 text-ellipsis overflow-hidden'>
                <a
                  href={user.linkedinProfileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800"
                >
                  {user.linkedinProfileUrl}
                </a>
              </TableCell>
              <TableCell className='max-w-96 text-ellipsis overflow-hidden'>
                <a
                  href={user.xProfileUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800"
                >
                  {user.xProfileUrl}
                </a>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination */}
      <div className='mt-10'>
        <Pagination>
          <PaginationContent>
            {/* Previous Button */}
            <PaginationItem className="cursor-pointer">
              <PaginationPrevious
                onClick={() => setPageNumber((prev) => Math.max(1, prev - 1))}
                className={pageNumber === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === 1}
              >
                Prev
              </PaginationPrevious>
            </PaginationItem>

            {/* First page */}
            <PaginationItem className="cursor-pointer">
              <PaginationLink
                onClick={() => setPageNumber(1)}
                isActive={pageNumber === 1}
              >
                1
              </PaginationLink>
            </PaginationItem>

            {/* Ellipsis before the current range */}
            {pageNumber > 3 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Dynamic Page Numbers */}
            {Array.from({ length: 3 }, (_, index) => {
              const page = pageNumber - 1 + index;
              if (page > 1 && page < totalPages) {
                return (
                  <PaginationItem className="cursor-pointer" key={page}>
                    <PaginationLink
                      isActive={pageNumber === page}
                      onClick={() => setPageNumber(page)}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              }
              return null;
            })}

            {/* Ellipsis after the current range */}
            {pageNumber < totalPages - 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Last page */}
            {totalPages > 1 && (
              <PaginationItem className="cursor-pointer">
                <PaginationLink
                  onClick={() => setPageNumber(totalPages)}
                  isActive={pageNumber === totalPages}
                >
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            )}

            {/* Next Button */}
            <PaginationItem className="cursor-pointer">
              <PaginationNext
                onClick={() => setPageNumber((prev) => Math.min(totalPages, prev + 1))}
                className={pageNumber === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === totalPages}
              >
                Next
              </PaginationNext>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
};

export default UserSocialMedia;






