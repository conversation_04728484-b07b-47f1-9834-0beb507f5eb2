import Card from '@/components/Card';
import { domain2, token } from '@/lib/utils';
import useCompanyStore from '@/store/companyStore';
import axios from 'axios';
import React, { useEffect } from 'react';

const Dashboard: React.FC = () => {

  const companyStore = useCompanyStore((state) => state);

  useEffect(() => {
    axios.get(`${domain2}/api/mapping/v1/company-master/all-company`, {
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      }
    }).then(res => {
      companyStore.setAllCompanies(res.data.data.companies, res.data.data.totalCompanies);
    });
  }, []);
  return (
    <div className=''>
      <div className='max-w-60'>
      <Card title='Companies' count={companyStore.totalCompanies} to='/companies' className='max-w-60' />
      </div>
      {/* <Spinner /> */}
    </div>
  )
}

export default Dashboard;