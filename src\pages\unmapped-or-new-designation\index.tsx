import React, { useEffect, useState } from 'react';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";

import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";

import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";

import { Input } from '@/components/ui/input';
import axios from 'axios';
import { domain2 } from '@/lib/utils';
import { MapDesignationType } from '@/types';
import { Button } from '@/components/ui/button';
import { Plus, Save, Trash } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import Spinner from '@/components/Spinner';

const UnmappedOrNewDesignation: React.FC = () => {
    const [itemsPerPage, setItemPerPage] = useState<number>(10);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [_, setDesignationFilter] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(false);
    const [totalPages, setTotalPages] = useState<number>(1);
    const [addDesignationName, setAddDesignationName] = useState<string>("");
    const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
    const [designationData, setDesignationData] = useState<MapDesignationType[] | undefined>(undefined);
    const [allDesignations, setAllDesignations] = useState<MapDesignationType[] | undefined>(undefined);

    const fetchMapDesignations = async () => {
        setLoading(true);
        try {
            const res = await axios.post(`${domain2}/api/mapping/v1/designation-master/get-all-unmapped-or-new-designation`);

            const fetchedDesignations = res.data.data;
            setAllDesignations(fetchedDesignations);
            
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            setDesignationData(fetchedDesignations.slice(startIndex, endIndex));

            setTotalPages(Math.ceil(fetchedDesignations.length / itemsPerPage));
        } catch (error) {
            console.error("Error fetching designations:", error);
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        fetchMapDesignations();
    }, []);

    useEffect(() => {
        if (allDesignations) {
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            setDesignationData(allDesignations.slice(startIndex, endIndex));
            setTotalPages(Math.ceil(allDesignations.length / itemsPerPage));
        }
    }, [currentPage, itemsPerPage, allDesignations]);

    const handleAddDesignation = async () => {
        try {
            setIsDialogOpen(false);
            setLoading(true);
            const response = await axios.post(`${domain2}/api/mapping/v1/designation-master/add-unmapped-or-new-designation`, {
                designation: addDesignationName
            }, {
                headers: {
                    "Content-Type": "application/json"
                }
            })

            if (response.data.status) {
                setAddDesignationName("");
                fetchMapDesignations();
                toast({
                    title: response.data.message,
                    variant: "default",
                    className: "bg-green-800 text-white"
                });
            } else {
                toast({
                    title: response.data.message,
                    variant: "destructive",
                });
            }
        } catch (error) {
            toast({
                title: "Failed to add designation",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    }

    const addToNewDesignation = async (designation: string) => {
        try {
            setLoading(true);
            const res = await axios.post(`${domain2}/api/mapping/v1/designation-master/add-designation`, {
                designation, mappedTo: []
            });

            if (res.data.status) {
                toast({
                    title: res.data.message,
                    description: "Designation added successfully",
                    variant: "default",
                    className: "bg-green-800 text-white"
                });
                setDesignationData(undefined);
                fetchMapDesignations();
            } else {
                toast({
                    title: res.data.message,
                    variant: "destructive",
                });
            }
        } catch (err) {
            let errorMessage = "Something went wrong";
            if (axios.isAxiosError(err) && err.response) {
                errorMessage = err.response.data.message || err.message;
            } else if (err instanceof Error) {
                errorMessage = err.message;
            }
            toast({
                title: errorMessage,
                description: "Failed to add designation",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    }

    const handleDelete = async (id: string) => {
        try {
            setLoading(true);
            const res = await axios.delete(`${domain2}/api/mapping/v1/designation-master/delete-unmapped-or-new-designation/${id}`);
            if (res.data.status) {
                setDesignationData(undefined);
                fetchMapDesignations();
                toast({
                    title: res.data.message,
                    variant: "default",
                    className: "bg-green-800 capitalize text-white"
                });
            }
        } catch (error) {
            toast({
                title: "Failed to delete designation",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    }

    const handleAddToUnmapped = async (designation: string) => {
        try {
            setLoading(true);
            const res = await axios.post(`${domain2}/api/mapping/v1/designation-master/add-unmapped-designation`, {
                designation,
                isMapped: false,
                mappedWith: ""
            });

            if (res.data.status) {
                // setDesignationData(undefined);
                fetchMapDesignations();
                toast({
                    title: res.data.message,
                    variant: "default",
                    className: "bg-green-800 text-white"
                });
            } else {
                toast({
                    title: res.data.message,
                    variant: "destructive",
                });
            }
        } catch (error) {
            toast({
                title: "Failed to add to unmapped",
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    }


    if (loading) {
        return <div className='flex justify-center items-center h-screen'>
            <Spinner />
        </div>
    }

    return (
        <div>
            <div className='flex justify-between items-center mt-5 w-full mb-3'>
                <div className="flex gap-3">
                    <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemPerPage(Number(value))}>
                        <SelectTrigger className="w-fit">
                            <SelectValue placeholder="Show Rows" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectLabel>Select Rows</SelectLabel>
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="25">25</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>

                    <Input
                        type="text"
                        className="max-w-60"
                        onChange={(e) => setDesignationFilter(e.target.value)}
                        placeholder="Filter by designation name"
                    />
                </div>
                <div>
                    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                            <Button className="bg-brandPrimary hover:bg-brandPrimaryDark text-white">
                                Add Variation <Plus />
                            </Button>
                        </DialogTrigger>

                        <DialogContent className="max-w-md max-h-[30rem] overflow-scroll py-0 px-3 sm:px-5">
                            <DialogTitle className="text-2xl text-center mb-5 sticky top-0 bg-background w-full py-5">
                                Add Designation
                            </DialogTitle>

                            <Input
                                type="text"
                                className="max-w-full w-full"
                                onChange={(e) => setAddDesignationName(e.target.value)}
                                placeholder="Designation Name"
                            />

                            <div className="py-5 bg-background sticky bottom-0">
                                <Button onClick={handleAddDesignation} className="bg-brandPrimary hover:bg-brandPrimaryDark text-white w-full">
                                    Add Designation <Save />
                                </Button>
                            </div>
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            <div>
                <Table className='text-base'>
                    <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
                        <TableRow>
                            <TableHead className='text-white'>S.No</TableHead>
                            <TableHead className='text-white'>Designation Name</TableHead>
                            <TableHead className="text-white text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {designationData?.map((data, index) => (
                            <TableRow key={data._id}>
                                <TableCell>{(currentPage - 1) * itemsPerPage + index + 1}</TableCell>
                                <TableCell>{data.designation}</TableCell>
                                <TableCell className='text-right flex items-center justify-end gap-x-3'>
                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button className='px-3 py-1 text-sm bg-green-700 hover:bg-green-800 text-white'>Add To New</Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    Do you really want to add {data.designation} into unmapped database ?
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => addToNewDesignation(data.designation)} className='text-sm bg-green-700 hover:bg-green-800 px-3 py-1 text-white'>Continue</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>

                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button className='px-3 py-1 text-sm bg-orange-600 hover:bg-orange-700 text-white'>Add To Unmapped</Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    Do you really want to add {data.designation} into unmapped database ?
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleAddToUnmapped(data.designation)} className='text-sm bg-orange-600 hover:bg-orange-700 px-3 py-1 text-white'>Continue</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>

                                    <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            {/* <Button className='px-3 py-1 text-sm bg-red-700 hover:bg-red-800 text-white'>Delete</Button> */}
                                            <Button
                                                variant={"ghost"}
                                                className="p-3 ml-2 h-5 w-5 rounded-full text-white hover:text-white hover:bg-red-600/50 bg-red-500"
                                            >
                                                <Trash />
                                            </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    Do you really want to delete {data.designation} from unmapped database ?
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleDelete(data._id)} className='text-sm bg-red-700 hover:bg-red-800 px-3 py-1 text-white'>Continue</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog>

                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
                
                {totalPages > 1 && (
                    <div className="mt-4 flex justify-center">
                        <Pagination>
                            <PaginationContent>
                                <PaginationItem>
                                    <PaginationPrevious 
                                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                        className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                                    />
                                </PaginationItem>
                                
                                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => {
                                    // Show first page, last page, and pages around current page
                                    if (
                                        page === 1 || 
                                        page === totalPages || 
                                        (page >= currentPage - 1 && page <= currentPage + 1)
                                    ) {
                                        return (
                                            <PaginationItem key={page}>
                                                <PaginationLink 
                                                    onClick={() => setCurrentPage(page)}
                                                    isActive={currentPage === page}
                                                >
                                                    {page}
                                                </PaginationLink>
                                            </PaginationItem>
                                        );
                                    }
                                    
                                    // Show ellipsis for gaps
                                    if (
                                        (page === 2 && currentPage > 3) || 
                                        (page === totalPages - 1 && currentPage < totalPages - 2)
                                    ) {
                                        return (
                                            <PaginationItem key={page}>
                                                <PaginationEllipsis />
                                            </PaginationItem>
                                        );
                                    }
                                    
                                    return null;
                                })}
                                
                                <PaginationItem>
                                    <PaginationNext 
                                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                        className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                                    />
                                </PaginationItem>
                            </PaginationContent>
                        </Pagination>
                    </div>
                )}
            </div>
        </div>
    )
}

export default UnmappedOrNewDesignation;
