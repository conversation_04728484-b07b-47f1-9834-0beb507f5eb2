import { Plus } from "lucide-react";
import React, { useCallback } from "react";
import { useDropzone } from "react-dropzone";

interface DropZoneProps {
    className?: string;
}

const DropZone: React.FC<DropZoneProps> = (props) => {
    const onDrop = useCallback((acceptedFiles: File[]) => {
        if (acceptedFiles.length > 0) {
            console.log(acceptedFiles[0]); // Log the first (and only) file
        }
    }, []);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        maxFiles: 1, // Limit to one file
        multiple: false, // Prevent selecting multiple files
    });

    return (
        <div
            className={`border-2 text-xl border-dashed p-5 text-center cursor-pointer text-neutral-500 border-neutral-500 w-96 max-h-96 mx-auto rounded-xl !${props.className}`}
            {...getRootProps()}
        >
            <input {...getInputProps()} />
            {isDragActive ? (
                <p>Drop your file here ...</p>
            ) : (
                <div className="w-full h-full relative">
                    <div className="mx-auto absolute left-0 right-0 top-1/2 -translate-y-1/2">
                        <p>Drag 'n' drop a file here, or click to select a file</p>
                        <Plus height={72} width={72} className="mx-auto"/>
                    </div>
                </div>

            )}
        </div>
    );
};

export default DropZone;
