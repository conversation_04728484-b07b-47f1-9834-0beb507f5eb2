import "./utils.css";
import { Routes, Route, Navigate } from "react-router-dom";
import Login from "./pages/login";
import Dashboard from "./pages/dashboard";
import Layout from "./layout";
import UploadExcel from "./pages/upload-excel";
import ViewExcel from "./pages/view-excel";

import Industires from "./pages/industries";
import Companies from "./pages/companies";
import Countries from "./pages/countries";
import States from "./pages/states";
import AllUsers from "./pages/all-users";
import NotificationReports from "./pages/notification-reports";
import ReportedProfile from "./pages/reported-profile";
import ReportedConcerns from "./pages/reported-concerns";
import Profile from "./pages/profile";
import useAuthStore from "./store/authStore";
import MapCompany from "./pages/map-company";
import UnmappedOrNewCompany from "./pages/unmapped-or-new-company";
import Designations from "./pages/designations";
import UnmappedOrNewDesignation from "./pages/unmapped-or-new-designation";
import MapDesignations from "./pages/map-designations";
import Publishers from "./pages/publishers";
import ViewPeople from "./pages/view-people";
import UserSocialMedia from "./pages/user-social-media";
import Institutes from "./pages/institutes";
import MapInstitutes from "./pages/map-institutes";
import UnmappedOrNewInstitute from "./pages/unmapped-or-new-institute";
import AudienceAcquisition from "./pages/vendors/audience-acquisition";
import Gifting from "./pages/vendors/gifiting";
import EventSetup from "./pages/vendors/event-setup";

function App() {

  const isAuthenticated: boolean = useAuthStore((state) => state.isAuthenticated);

  return (
    <>
      <Routes>
        {/* Without Layout Before Login */}
        <Route path="/login" element={!isAuthenticated ? <Login /> : <Navigate to="/" />} />

        {/* With Layout Structure After Login */}
        <Route element={isAuthenticated ? <Layout /> : <Navigate to={"/login"} />}>
          <Route path="/" element={<Dashboard />} />
          <Route path="/profile" element={<Profile />} />
          <Route path="/industries" element={<Industires />} />
          <Route path="/institutes" element={<Institutes />} />
          <Route path="/companies" element={<Companies />} />
          <Route path="/map-company" element={<MapCompany />} />
          <Route path="/map-designations" element={<MapDesignations />} />
          <Route path="/map-institutes" element={<MapInstitutes />} />
          <Route path="/unmapped-or-new-institute" element={<UnmappedOrNewInstitute />} />
          <Route path="/unmapped-or-new-company" element={<UnmappedOrNewCompany />} />
          <Route path="/unmapped-or-new-designation" element={<UnmappedOrNewDesignation />} />
          <Route path="/user-social-media" element={<UserSocialMedia />} />
          <Route path="/publishers" element={<Publishers />} />
          <Route path="/designations" element={<Designations />} />
          <Route path="/countries" element={<Countries />} />
          <Route path="/states" element={<States />} />
          <Route path="/upload-excel" element={<UploadExcel />} />
          <Route path="/view-excel" element={<ViewExcel />} />
          <Route path="/view-people" element={<ViewPeople />} />
          <Route path="/users" element={<AllUsers />} />
          <Route path="/notification-reports" element={<NotificationReports />} />
          <Route path="/reported-profiles" element={<ReportedProfile />} />
          <Route path="/reported-concerns" element={<ReportedConcerns />} />
          <Route path="/vendors/audience-acquisition" element={<AudienceAcquisition />} />
          <Route path="/vendors/gifting" element={<Gifting />} />
          <Route path="/vendors/event-setup" element={<EventSetup />} />
        </Route>
      </Routes>
    </>
  )
}

export default App;
