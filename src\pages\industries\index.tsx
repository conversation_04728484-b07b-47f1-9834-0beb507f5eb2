import { Input } from '@/components/ui/input';
import React, { useEffect, useState } from 'react';

import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import AddDialogBox from '@/components/AddDialogBox';
import MapDialogBox from '@/components/MapDialogBox';
import axios from 'axios';
import { ApiType } from '@/types';
import { domain } from '@/lib/utils';

const Industires: React.FC = () => {

  const [industryData, setIndustryData] = useState<ApiType[] | undefined>(undefined);
  const [filteredData, setFilteredData] = useState<ApiType[] | undefined>(undefined);

  useEffect(() => {
    axios.get(`${domain}/mapping/api/industries`).then(res => {
      console.log("The data is: ", res.data.data);
      setIndustryData(res.data.data);
      setFilteredData(res.data.data); // Initialize filtered data
    });
  }, []);

  const filterIndustries = (event: React.ChangeEvent<HTMLInputElement>) => {
    const query = event.target.value.toLowerCase();
    const filtered = industryData?.filter((industry) =>
      industry.name.toLowerCase().includes(query)
    );
    setFilteredData(filtered);
  };

  const print = (): void => console.log("Button Clicked");

  return (
    <div className='w-full'>
      <div className='flex justify-between mt-5 w-full mb-3'>
        <Input
          type='text'
          className='max-w-60'
          onChange={filterIndustries}
          placeholder='Filter by industry name'
        />
        <div className='flex gap-3'>
          <AddDialogBox onSubmit={print} title='Add Industry' placeholder="Enter your industry name" buttonText='Add Industry' />
          <MapDialogBox onSubmit={print} title='Map Industry' placeholder="Enter your industry name" mapPlaceholder="Enter your mapping name" buttonText='Map Industry' />
        </div>
      </div>

      <div>
        <Table className='text-base'>
          <TableCaption>A list of your all industries.</TableCaption>
          <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
            <TableRow>
              <TableHead className="w-[100px] text-white">Id</TableHead>
              <TableHead className='text-white'>Industry Name</TableHead>
              <TableHead className='text-white'>Parent</TableHead>
              <TableHead className="text-right text-white">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData?.map((data) => (
              <TableRow key={data.id}>
                <TableCell className="font-medium">{data.id}</TableCell>
                <TableCell>{data.name}</TableCell>
                <TableCell>{data.parent_id}</TableCell>
                <TableCell className="flex justify-end gap-2">

                  {/* <EditDialogBox onSubmit={print} value={data.name} title='Edit Industry' /> */}
                  {/* <AlertDialogBox /> */}

                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default Industires;
