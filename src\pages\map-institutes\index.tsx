import React, { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Institute, MapInstituteType } from '@/types';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
  DialogHeader,
  DialogFooter,
} from "@/components/ui/dialog";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import { DialogDescription } from '@radix-ui/react-dialog';

import Spinner from '@/components/Spinner'; // Import Spinner component for loading indicator
import axios from 'axios';
import { domain2 } from '@/lib/utils';
import { SquarePen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from '@/hooks/use-toast';

const MapInstitutes: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [allInstitutes, setAllInstitutes] = useState<Institute[] | undefined>(undefined);
  const [mapInstituteData, setMapInstituteData] = useState<MapInstituteType[] | undefined>(undefined);
  const [filteredInstituteData, setFilteredInstituteData] = useState<MapInstituteType[] | undefined>(undefined);
  const [pageNumber, setPageNumber] = useState<number>(1); // Start with page 1
  const [itemsPerPage, setItemPerPage] = useState<number>(10);
  const [instituteFilter, setInstituteFilter] = useState<string>("");
  const [instituteSearch, setInstituteSearch] = useState<string>("");
  const [selectedInstitute, setSelectedInstitute] = useState<string | null>(null); // Allow null for validation
  const [instituteError, setInstituteError] = useState<string>(''); // State for error message
  const [filterType, setFilterType] = useState<string>("all"); // Add filter type state

  // Calculate total pages based on filtered data length and items per page
  const totalPages = filteredInstituteData ? Math.ceil(filteredInstituteData.length / itemsPerPage) : 1;

  const fetchMapInstitutes = async () => {
    setLoading(true);
    try {
      const res = await axios.get(`${domain2}/api/mapping/v1/education-master/get-unmapped-institute`, {
        headers: {
          "Content-Type": "application/json"
        }
      });

      const fetchedInstitutes = res.data.data;
      setMapInstituteData(fetchedInstitutes);
      setFilteredInstituteData(fetchedInstitutes); // Set the fetched data to be initially displayed
    } catch (error) {
      console.error("Error fetching institutes:", error);
    } finally {
      setLoading(false);
    }
  }

  const fetchInstitutes = async (search?: string) => {
    const res = await axios.get(`${domain2}/api/mapping/v1/education-master/all-institute?search=${search || ""}`, {
      headers: {
        "Content-Type": "application/json"
      }
    });
    const fetchedInstitutes = res.data.data.institutes;
    setAllInstitutes(fetchedInstitutes);
  }

  useEffect(() => {
    fetchMapInstitutes();
  }, []);

  useEffect(() => {
    fetchInstitutes(instituteSearch);
  }, [instituteSearch]);

  useEffect(() => {
    let filteredData = mapInstituteData;

    // First apply mapped/unmapped filter
    if (filterType === "mapped") {
      filteredData = mapInstituteData?.filter(institute => institute.isMapped);
    } else if (filterType === "unmapped") {
      filteredData = mapInstituteData?.filter(institute => !institute.isMapped);
    }

    // Then apply institute name filter
    if (instituteFilter) {
      filteredData = filteredData?.filter(institute =>
        institute.institute.toLowerCase().includes(instituteFilter.toLowerCase())
      );
    }

    setFilteredInstituteData(filteredData);
    setPageNumber(1); // Reset to first page when filtering
  }, [instituteFilter, mapInstituteData, filterType]);

  const handleMapChange = async (unmappedInstitute: string) => {
    if (!selectedInstitute) {
      setInstituteError('Institute must be selected');
      return;
    }
    setLoading(true);
    const response = await axios.post(`${domain2}/api/mapping/v1/education-master/mapping-unmapped-institute`, {
      unmappedInstitute, mappedInstitute: selectedInstitute
    }, {
      headers: {
        "Content-Type": "application/json"
      }
    });

    setInstituteFilter("");
    setInstituteError(''); // Reset error

    setLoading(false);

    if (response.data.status) {
      toast({
        title: response.data.message || "Institue Mapped Successfully",
        className: "bg-green-800 text-white",
      });
    } else {
      toast({
        title: "Something Went Wrong",
        variant: "destructive",
      });
    }

    fetchMapInstitutes();
  }

  const handleSendBack = async (institute: string) => {
    setLoading(true);
    const response = await axios.post(`${domain2}/api/mapping/v1/education-master/send-back-unmapped-to-unmapped-or-new-institute`, {
      institute
    }, {
      headers: {
        "Content-Type": "application/json"
      }
    });

    if (response.data.status) {
      fetchMapInstitutes();
      toast({
        title: response.data.message,
        className: "bg-green-800 text-white"
      });
    } else {
      toast({
        title: response.data.message,
        variant: "destructive"
      });
    }
    setLoading(false);
  }

  // const handleAddCompany = async () => {
  //     setLoading(true);
  //     try {
  //         const response = await axios.post(`${domain2}/api/mapping/v1/company-master/add-unmapped-or-new-company`, {
  //             company: addCompanyName.toLowerCase()
  //         }, {
  //             headers: {
  //                 "Content-Type": "application/json"
  //             }
  //         });

  //         if (response.data.status) {
  //             toast({
  //                 title: "Company Added Successfully",
  //                 className: "bg-green-800 text-white"
  //             });
  //         } else {
  //             toast({
  //                 title: response.data.message,
  //                 variant: "destructive"
  //             });
  //         }
  //     } catch (error) {
  //         toast({
  //             title: "Something went wrong!",
  //             variant: "destructive"
  //         });
  //     } finally {
  //         setLoading(false);
  //     }
  // }

  const handleUnmappedInstitute = async (institute: string, mappedWith: string) => {
    try {
      setLoading(true);
      const response = await axios.post(`${domain2}/api/mapping/v1/education-master/unmapped-mapped-institute`, {
        institute,
        mappedWith,
      }, {
        headers: {
          "Content-Type": "application/json"
        }
      });

      if (response.data.status) {
        toast({
          title: "Company Unmapped Successfully",
          className: "bg-green-800 text-white"
        });
      } else {
        toast({
          title: response.data.message,
          variant: "destructive"
        });
      }

    } catch (error) {
      toast({
        title: "Something went wrong!",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      fetchMapInstitutes();
    }
  }

  if (loading) {
    return <Spinner />
  }

  return (
    <div className='w-full'>
      <div className="flex justify-between items-center gap-3 mt-5 w-full mb-3">
        <div className='flex gap-3'>
          <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemPerPage(Number(value))}>
            <SelectTrigger className="w-fit">
              <SelectValue placeholder="Show Rows" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Select Rows</SelectLabel>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>

          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-fit">
              <SelectValue placeholder="Filter By Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Mapped or Unmapped</SelectLabel>
                <SelectItem value="all" className='cursor-pointer'>All</SelectItem>
                <SelectItem value="mapped" className='cursor-pointer'>Mapped</SelectItem>
                <SelectItem value="unmapped" className='cursor-pointer'>Unmapped</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>

          <Input
            type="text"
            className="max-w-60"
            onChange={(e) => setInstituteFilter(e.target.value)}
            placeholder="Filter by institute name"
          />
        </div>


        {/* <Dialog>
                    <DialogTrigger asChild>
                        <Button className="bg-brandPrimary hover:bg-brandPrimaryDark text-white">
                            Add Company <Plus />
                        </Button>
                    </DialogTrigger>

                    <DialogContent className="max-w-md max-h-[30rem] overflow-scroll py-0 px-3 sm:px-5">
                        <DialogTitle className="text-2xl text-center mb-5 sticky top-0 bg-background w-full py-5">
                            Add Company
                        </DialogTitle>

                        <Input
                            type="text"
                            className="max-w-full w-full"
                            onChange={(e) => setAddCompanyName(e.target.value)}
                            placeholder="Company Name"
                        />

                        <div className="py-5 bg-background sticky bottom-0">
                            <Button onClick={handleAddCompany} className="bg-brandPrimary hover:bg-brandPrimaryDark text-white w-full">
                                Add Company <Save />
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog> */}
      </div>

      {/* Table Data */}
      <div>
        <Table className='text-base'>
          <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
            <TableRow>
              <TableHead className='text-white'>S.No</TableHead>
              <TableHead className='text-white'>Institute Name</TableHead>
              <TableHead className='text-white'>Mapped With</TableHead>
              <TableHead className="text-white text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredInstituteData?.slice((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage).map((data, index) => (
              <TableRow key={data._id}>
                <TableCell>{((pageNumber - 1) * itemsPerPage) + index + 1}</TableCell>
                <TableCell>{data.institute}</TableCell>
                <TableCell>{data.mappedWith}</TableCell>
                <TableCell className='text-right'>

                  <Dialog onOpenChange={(open) => {
                      if (open) {
                        setInstituteSearch('');
                        setSelectedInstitute(null);
                      }
                    }}>
                    <DialogTrigger asChild>
                      {!data.isMapped && <Button
                        variant={"ghost"}
                        className="p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-cyan-600/50 bg-cyan-500"
                      >
                        <SquarePen />
                      </Button>}

                    </DialogTrigger>

                    <DialogContent className="max-w-xl overflow-scroll">
                      <DialogHeader>
                        <DialogTitle className="text-2xl text-center sticky top-0 bg-background w-full">
                          {data.institute}
                        </DialogTitle>
                        <DialogDescription>
                        </DialogDescription>
                      </DialogHeader>
                      <div className="grid gap-5">
                        <div className="w-full">
                          <Label
                            htmlFor="company"
                            className="text-sm text-muted-foreground font-normal"
                          >
                            Institute Name
                          </Label>
                          <Input
                            id="institute"
                            type="text"
                            value={data.institute}
                            readOnly
                            placeholder="Institute Name"
                          />
                        </div>

                        <div className='w-full'>
                          <Label htmlFor="institute_name" className='text-primary'>Map With <span className='text-destructive'>*</span></Label>
                          <Input
                            id="institute_name"
                            type="text"
                            value={instituteSearch}
                            onChange={(e) => {
                              setInstituteSearch(e.target.value);
                              setSelectedInstitute(e.target.value);
                            }}
                            placeholder="Search Institute Name"
                            className={instituteError ? 'border-red-500' : ''}
                          />

                          {instituteError && <p className="text-sm text-red-500 mt-2">{instituteError}</p>}

                          <div className='h-60 p-2 border border-muted mt-2 rounded-md overflow-scroll'>
                            {allInstitutes
                              ?.filter(institute => 
                                institute.name.toLowerCase().includes(instituteSearch.toLowerCase())
                              )
                              .map((institute, index) => (
                                <p
                                  onClick={() => {
                                    setSelectedInstitute(institute.name);
                                    setInstituteSearch(institute.name);
                                    setInstituteError('');
                                  }}
                                  className='p-2 cursor-pointer text-sm rounded-md hover:bg-muted'
                                  key={institute._id || index}
                                >
                                  {institute.name}
                                </p>
                              ))}
                          </div>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button 
                          type="button" 
                          onClick={() => {
                            if (selectedInstitute) {
                              handleMapChange(data.institute);
                              setInstituteSearch('');
                              setSelectedInstitute(null);
                            } else {
                              setInstituteError('Please select an institute');
                            }
                          }} 
                          className='bg-cyan-600 hover:bg-cyan-700 text-white'
                          disabled={!selectedInstitute}
                        >
                          Save changes
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>

                  {/* <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button
                                                variant={"ghost"}
                                                className="p-3 ml-2 h-5 w-5 rounded-full text-white hover:text-white hover:bg-red-600/50 bg-red-500"
                                            >
                                                <Trash />
                                            </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    This action cannot be undone. This will permanently delete the company.
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleDelete(data._id)}>Continue</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog> */}

                  {!data.isMapped ? <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button className='px-3 py-1 ml-3 text-sm bg-brandPrimary hover:bg-brandPrimaryDark text-white w-24'>Send Back</Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure want to send {data.institute} back to unmapped/new pool?
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleSendBack(data.institute)} className="bg-brandPrimary hover:bg-brandPrimaryDark text-white">Continue</AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog> :
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button className='px-3 py-1 ml-3 text-sm bg-orange-600 hover:bg-orange-700 text-white w-24'>Unmap</Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                          <AlertDialogDescription>
                            Do you really want to add {data.institute} into unmapped database ?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleUnmappedInstitute(data.institute, data.mappedWith)} className='text-sm bg-orange-600 hover:bg-orange-700 px-3 py-1 text-white'>Continue</AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  }
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className='mt-10'>
        <Pagination>
          <PaginationContent>
            <PaginationItem className="cursor-pointer">
              <PaginationPrevious
                onClick={() => setPageNumber((prev) => Math.max(1, prev - 1))}
                className={pageNumber === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === 1}
              >
                Prev
              </PaginationPrevious>
            </PaginationItem>

            <PaginationItem className="cursor-pointer">
              <PaginationLink
                onClick={() => setPageNumber(1)}
                isActive={pageNumber === 1}
              >
                1
              </PaginationLink>
            </PaginationItem>

            {pageNumber > 3 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {Array.from({ length: 3 }, (_, index) => {
              const page = pageNumber - 1 + index;
              if (page > 1 && page < totalPages) {
                return (
                  <PaginationItem className="cursor-pointer" key={page}>
                    <PaginationLink
                      isActive={pageNumber === page}
                      onClick={() => setPageNumber(page)}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              }
              return null;
            })}

            {pageNumber < totalPages - 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {totalPages > 1 && (
              <PaginationItem className="cursor-pointer">
                <PaginationLink
                  onClick={() => setPageNumber(totalPages)}
                  isActive={pageNumber === totalPages}
                >
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            )}

            <PaginationItem className="cursor-pointer">
              <PaginationNext
                onClick={() => setPageNumber((prev) => Math.min(totalPages, prev + 1))}
                className={pageNumber === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === totalPages}
              >
                Next
              </PaginationNext>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
}

export default MapInstitutes;
