import { BlockRecord } from "@/types";
import { create } from "zustand";
import { persist, PersistOptions } from "zustand/middleware";

// Define the types
interface BlockRecords {
  concernProfiles: BlockRecord[];
  setConcernProfiles: (concerns: BlockRecord[]) => void;

  blockedUsers: BlockRecord[];
  setBlockedUsers: (blocked: BlockRecord[]) => void;

  reportedProfiles: BlockRecord[];
  setReportedProfiles: (reported: BlockRecord[]) => void;
}

// Create the store
const useBlockRecords = create<BlockRecords>()(
  persist(
    (set) => ({
      concernProfiles: [],
      setConcernProfiles: (concerns: BlockRecord[]) =>
        set(() => ({
          concernProfiles: concerns,
        })),

      blockedUsers: [],
      setBlockedUsers: (blocked: BlockRecord[]) =>
        set(() => ({
          blockedUsers: blocked,
        })),

      reportedProfiles: [],
      setReportedProfiles: (reported: BlockRecord[]) =>
        set(() => ({
          reportedProfiles: reported,
        })),
    }),
    {
      name: "blockUsers-storage", // Unique name for persistence
    } as PersistOptions<BlockRecords>
  )
);

export default useBlockRecords;
