import React from 'react';
import { Link } from 'react-router-dom';

interface CardProps {
    title: string;
    count: number;
    className?: string;
    to?: string;
}

const Card: React.FC<CardProps> = (props) => {
    return (
        props.to ?
            <Link to={props.to}>
                <div className={`p-5 w-full min-h-20 rounded-lg bg-brandPrimary/10 text-brandPrimary border-2 border-brandPrimary !${props.className}`}>
                    <span className='mb-5 inline-block font-medium'>{props.title}</span>
                    <h3 className='font-semibold text-xl'>{props.count}</h3>
                </div>
            </Link>
            :
            <div className={`p-5 w-full min-h-20 rounded-lg bg-brandPrimary/10 text-brandPrimary border-2 border-brandPrimary !${props.className}`}>
                <span className='mb-5 inline-block font-medium'>{props.title}</span>
                <h3 className='font-semibold text-xl'>{props.count}</h3>
            </div>
    )
}

export default Card;