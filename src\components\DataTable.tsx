import React from 'react';

import {
    Table,
    TableBody,
    TableCaption,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";

interface DataTableProps {
    headers: string[];
    rowsData: string[];
}

const DataTable: React.FC<DataTableProps> = (props) => {
    return (
        <Table>
            <TableCaption>A list of your recent invoices.</TableCaption>
            <TableHeader>
                <TableRow>
                    {props.headers.map((head: string, index: number) => (
                        <TableHead key={index}>{head}</TableHead>
                    ))}
                </TableRow>
            </TableHeader>
            <TableBody>
                {
                    props.rowsData.map(() => (
                        <TableRow>
                            <TableCell className="font-medium">INV001</TableCell>
                            <TableCell>Paid</TableCell>
                            <TableCell>Credit Card</TableCell>
                            <TableCell className="text-right">$250.00</TableCell>
                        </TableRow>
                    ))
                }
            </TableBody>
        </Table>

    )
}

export default DataTable;