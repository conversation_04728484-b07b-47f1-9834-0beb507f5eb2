import { create } from 'zustand';
import { VendorCompanyType } from '@/types';
import axios from 'axios';

interface VendorStore {
    acquisitionCompany: VendorCompanyType[];
    giftingCompany: VendorCompanyType[];
    eventSetupCompany: VendorCompanyType[];
    setAcquisitionCompany: () => void;
    setGiftingCompany: () => void;
    setEventSetupCompany: () => void;
    loading: boolean;
}

const domain2 = "https://kettleworld.space";
const token = "Bearer 4872|W9hBxuIrteale3Mlwz1ToNhhKKkMehCdDNibpN9p"

const getVendorCompanies = async () => {
    try {
        const response = await axios.post(`${domain2}/api/get-audience-acquisition`, {}, {
            headers: {
                Authorization: token,
            },
        });
        return response.data.data;
    } catch (error) {
        console.error("Error fetching vendors:", error);
        return [];
    }
};

const getGiftingVendorCompanies = async () => {
    try {
        const response = await axios.post(`${domain2}/api/get-gifting-partner`, {}, {
            headers: {
                Authorization: token,
            },
        });
        return response.data.data;
    } catch (error) {
        console.error("Error fetching vendors:", error);
        return [];
    }
};

const getEventSetupVendorCompanies = async () => {
    try {
        const response = await axios.post(`${domain2}/api/get-event-setup`, {}, {
            headers: {
                Authorization: token,
            },
        });
        return response.data.data;
    } catch (error) {
        console.error("Error fetching vendors:", error);
        return [];
    }
};

const useVendorStore = create<VendorStore>((set) => (
    {
        acquisitionCompany: [],
        setAcquisitionCompany: async () => {
            set({ loading: true });
            const vendors = await getVendorCompanies();
            set({ acquisitionCompany: vendors, loading: false });
        },
        giftingCompany: [],
        setGiftingCompany: async () => {
            set({ loading: true });
            const vendors = await getGiftingVendorCompanies();
            set({ giftingCompany: vendors, loading: false });
        },
        eventSetupCompany: [],
        setEventSetupCompany: async () => {
            set({ loading: true });
            const vendors = await getEventSetupVendorCompanies();
            set({ eventSetupCompany: vendors, loading: false });
        },
        loading: false,
    }
));

export default useVendorStore;