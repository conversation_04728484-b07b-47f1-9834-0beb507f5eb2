import { BellDot, BookOpen, BriefcaseBusiness, Bug, Building2, FileUp, FlagOff, GitCompareArrows, Home, Map, Newspaper, PersonStanding, UsersRound, Twitter, School, UserCog, Gift, Goal, ChevronDown, ChevronUp } from "lucide-react"; // Add Twitter import
import { LucideProps } from "lucide-react"; // Import LucideProps for proper typing

// Define the structure of an item and subItem
export type Link = {
  title: string;
  url: string;
  icon: React.ComponentType<LucideProps>; // Updated to accept LucideProps
};

export type DropdownGroup = {
  title: string;
  icon: React.ComponentType<LucideProps>;
  items: Link[];
};

// Dropdown groups for sidebar navigation
export const dropdownGroups: DropdownGroup[] = [
  {
    title: "Companies",
    icon: Building2,
    items: [
      {
        title: "Companies",
        url: "/companies",
        icon: Building2,
      },
      {
        title: "Map Company",
        url: "/map-company",
        icon: Map
      },
      {
        title: "Unmapped or New Company",
        url: "/unmapped-or-new-company",
        icon: GitCompareArrows
      }
    ]
  },
  {
    title: "Designations",
    icon: BriefcaseBusiness,
    items: [
      {
        title: "Designations",
        url: "/designations",
        icon: BriefcaseBusiness,
      },
      {
        title: "Map Designations",
        url: "/map-designations",
        icon: Map
      },
      {
        title: "Unmapped or New Designation",
        url: "/unmapped-or-new-designation",
        icon: GitCompareArrows
      }
    ]
  },
  {
    title: "Institutes",
    icon: School,
    items: [
      {
        title: "Institutes",
        url: "/institutes",
        icon: School
      },
      {
        title: "Map Institutes",
        url: "/map-institutes",
        icon: Map
      },
      {
        title: "Unmapped or New Institute",
        url: "/unmapped-or-new-institute",
        icon: GitCompareArrows
      }
    ]
  },
  {
    title: "Vendors",
    icon: UserCog,
    items: [
      {
        title: "Audience Acquisition",
        url: "/vendors/audience-acquisition",
        icon: UserCog
      },
      {
        title: "Gifting Partners",
        url: "/vendors/gifting",
        icon: Gift
      },
      {
        title: "Event Setup",
        url: "/vendors/event-setup",
        icon: Goal
      }
    ]
  }
];

// Regular links that don't need dropdowns
export const regularLinks: Link[] = [
  {
    icon: Home,
    title: "Home",
    url: "/"
  },
  {
    title: "User LinkedIn/Twitter",
    url: "/user-social-media",
    icon: Twitter
  },
  {
    title: "Publishers",
    url: "/publishers",
    icon: Newspaper
  },
  {
    title: "Upload Excel",
    url: "/upload-excel",
    icon: FileUp,
  },
  {
    title: "View Excel",
    url: "/view-excel",
    icon: BookOpen,
  },
  {
    title: "View People",
    url: "/view-people",
    icon: PersonStanding,
  },
  {
    title: "All Users",
    url: "/users",
    icon: UsersRound,
  },
  {
    title: "Notification Reports",
    url: "/notification-reports",
    icon: BellDot,
  },
  {
    title: "Reported Profiles",
    url: "/reported-profiles",
    icon: Bug,
  },
  {
    title: "Reported Concerns",
    url: "/reported-concerns",
    icon: FlagOff,
  },
];

// Export ChevronDown and ChevronUp for use in sidebar
export { ChevronDown, ChevronUp };
