import React, { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Company, MapCompanyType } from '@/types';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";

import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";

import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogTrigger,
    DialogHeader,
    DialogFooter,
} from "@/components/ui/dialog";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import { DialogDescription } from '@radix-ui/react-dialog';

import Spinner from '@/components/Spinner'; // Import Spinner component for loading indicator
import axios from 'axios';
import { domain2 } from '@/lib/utils';
import { SquarePen } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from '@/hooks/use-toast';

const MapCompany: React.FC = () => {
    const [loading, setLoading] = useState<boolean>(false);
    const [allCompanies, setAllCompanies] = useState<Company[] | undefined>(undefined);
    const [mapCompanyData, setMapCompanyData] = useState<MapCompanyType[] | undefined>(undefined);
    const [filteredCompanyData, setFilteredCompanyData] = useState<MapCompanyType[] | undefined>(undefined);
    const [pageNumber, setPageNumber] = useState<number>(1); // Start with page 1
    const [itemsPerPage, setItemPerPage] = useState<number>(10);
    const [companyFilter, setCompanyFilter] = useState<string>("");
    const [companySearch, setCompanySearch] = useState<string>("");
    const [selectedCompany, setSelectedCompany] = useState<string | null>(null); // Allow null for validation
    const [companyError, setCompanyError] = useState<string>(''); // State for error message
    const [filterType, setFilterType] = useState<string>("all"); // Add filter type state

    // Calculate total pages based on filtered data length and items per page
    const totalPages = filteredCompanyData ? Math.ceil(filteredCompanyData.length / itemsPerPage) : 1;

    const fetchMapCompanies = async () => {
        setLoading(true);
        try {
            const res = await axios.get(`${domain2}/api/mapping/v1/company-master/get-unmapped-company`, {
                headers: {
                    "Content-Type": "application/json"
                }
            });

            const fetchedCompanies = res.data.data;
            setMapCompanyData(fetchedCompanies);
            setFilteredCompanyData(fetchedCompanies); // Set the fetched data to be initially displayed
        } catch (error) {
            console.error("Error fetching companies:", error);
        } finally {
            setLoading(false);
        }
    }

    const fetchCompanies = async (search?: string) => {
        const res = await axios.get(`${domain2}/api/mapping/v1/company-master/all-company?search=${search||""}`, {
            headers: {
                "Content-Type": "application/json"
            }
        });
        const fetchedCompanies = res.data.data.companies;
        setAllCompanies(fetchedCompanies);
    }

    useEffect(() => {
        fetchMapCompanies();
    }, []);

    useEffect(() => {
        fetchCompanies(companySearch);
    }, [companySearch]);

    useEffect(() => {
        let filteredData = mapCompanyData;

        // First apply mapped/unmapped filter
        if (filterType === "mapped") {
            filteredData = mapCompanyData?.filter(company => company.isMapped);
        } else if (filterType === "unmapped") {
            filteredData = mapCompanyData?.filter(company => !company.isMapped);
        }

        // Then apply company name filter
        if (companyFilter) {
            filteredData = filteredData?.filter(company =>
                company.company.toLowerCase().includes(companyFilter.toLowerCase())
            );
        }

        setFilteredCompanyData(filteredData);
        setPageNumber(1); // Reset to first page when filtering
    }, [companyFilter, mapCompanyData, filterType]);

    const handleMapChange = async (unmappedCompany: string) => {
        if (!selectedCompany) {
            setCompanyError('Company must be selected');
            return;
        }
        setLoading(true);
        const response = await axios.post(`${domain2}/api/mapping/v1/company-master/mapping-unmapped-company`, {
            unmappedCompany, mappedCompany: selectedCompany
        }, {
            headers: {
                "Content-Type": "application/json"
            }
        });

        setCompanyFilter("");
        setCompanyError(''); // Reset error

        setLoading(false);

        if (response.data.status) {
            toast({
                title: "Company Mapped Successfully",
                className: "bg-green-800 text-white",
            });
        } else {
            toast({
                title: "Something Went Wrong",
                variant: "destructive",
            });
        }

        fetchMapCompanies();
    }

    const handleSendBack = async (company: string) => {
        const response = await axios.post(`${domain2}/api/mapping/v1/company-master/send-back-unmapped-to-unmapped-or-new-company`, {
            company
        }, {
            headers: {
                "Content-Type": "application/json"
            }
        });

        if (response.data.status) {
            fetchMapCompanies();
            toast({
                title: response.data.message,
                className: "bg-green-800 text-white"
            });
        } else {
            toast({
                title: response.data.message,
                variant: "destructive"
            });
        }
    }

    // const handleAddCompany = async () => {
    //     setLoading(true);
    //     try {
    //         const response = await axios.post(`${domain2}/api/mapping/v1/company-master/add-unmapped-or-new-company`, {
    //             company: addCompanyName.toLowerCase()
    //         }, {
    //             headers: {
    //                 "Content-Type": "application/json"
    //             }
    //         });

    //         if (response.data.status) {
    //             toast({
    //                 title: "Company Added Successfully",
    //                 className: "bg-green-800 text-white"
    //             });
    //         } else {
    //             toast({
    //                 title: response.data.message,
    //                 variant: "destructive"
    //             });
    //         }
    //     } catch (error) {
    //         toast({
    //             title: "Something went wrong!",
    //             variant: "destructive"
    //         });
    //     } finally {
    //         setLoading(false);
    //     }
    // }

    const handleUnmappedCompany = async (company: string, mappedWith: string) => {
        try {
            setLoading(true);
            const response = await axios.post(`${domain2}/api/mapping/v1/company-master/unmapped-mapped-company`, {
                company,
                mappedWith,
            }, {
                headers: {
                    "Content-Type": "application/json"
                }
            });

            if (response.data.status) {
                toast({
                    title: "Company Unmapped Successfully",
                    className: "bg-green-800 text-white"
                });
            } else {
                toast({
                    title: response.data.message,
                    variant: "destructive"
                });
            }

        } catch (error) {
            toast({
                title: "Something went wrong!",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
            fetchMapCompanies();
        }
    }

    if (loading) {
        return <Spinner />
    }

    return (
        <div className='w-full'>
            <div className="flex justify-between items-center gap-3 mt-5 w-full mb-3">
                <div className='flex gap-3'>
                    <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemPerPage(Number(value))}>
                        <SelectTrigger className="w-fit">
                            <SelectValue placeholder="Show Rows" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectLabel>Select Rows</SelectLabel>
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="25">25</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>

                    <Select value={filterType} onValueChange={setFilterType}>
                        <SelectTrigger className="w-fit">
                            <SelectValue placeholder="Filter By Type" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectLabel>Mapped or Unmapped</SelectLabel>
                                <SelectItem value="all" className='cursor-pointer'>All</SelectItem>
                                <SelectItem value="mapped" className='cursor-pointer'>Mapped</SelectItem>
                                <SelectItem value="unmapped" className='cursor-pointer'>Unmapped</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>

                    <Input
                        type="text"
                        className="max-w-60"
                        onChange={(e) => setCompanyFilter(e.target.value)}
                        placeholder="Filter by company name"
                    />
                </div>


                {/* <Dialog>
                    <DialogTrigger asChild>
                        <Button className="bg-brandPrimary hover:bg-brandPrimaryDark text-white">
                            Add Company <Plus />
                        </Button>
                    </DialogTrigger>

                    <DialogContent className="max-w-md max-h-[30rem] overflow-scroll py-0 px-3 sm:px-5">
                        <DialogTitle className="text-2xl text-center mb-5 sticky top-0 bg-background w-full py-5">
                            Add Company
                        </DialogTitle>

                        <Input
                            type="text"
                            className="max-w-full w-full"
                            onChange={(e) => setAddCompanyName(e.target.value)}
                            placeholder="Company Name"
                        />

                        <div className="py-5 bg-background sticky bottom-0">
                            <Button onClick={handleAddCompany} className="bg-brandPrimary hover:bg-brandPrimaryDark text-white w-full">
                                Add Company <Save />
                            </Button>
                        </div>
                    </DialogContent>
                </Dialog> */}
            </div>

            {/* Table Data */}
            <div>
                <Table className='text-base'>
                    <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
                        <TableRow>
                            <TableHead className='text-white'>S.No</TableHead>
                            <TableHead className='text-white'>Company Name</TableHead>
                            <TableHead className='text-white'>Mapped With</TableHead>
                            <TableHead className="text-white text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {filteredCompanyData?.slice((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage).map((data, index) => (
                            <TableRow key={data._id}>
                                <TableCell>{((pageNumber - 1) * itemsPerPage) + index + 1}</TableCell>
                                <TableCell>{data.company}</TableCell>
                                <TableCell>{data.mappedWith}</TableCell>
                                <TableCell className='text-right'>

                                    <Dialog>
                                        <DialogTrigger asChild>
                                            {!data.isMapped && <Button
                                                variant={"ghost"}
                                                className="p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-cyan-600/50 bg-cyan-500"
                                            >
                                                <SquarePen />
                                            </Button>}

                                        </DialogTrigger>

                                        <DialogContent className="max-w-xl overflow-scroll">
                                            <DialogHeader>
                                                <DialogTitle className="text-2xl text-center sticky top-0 bg-background w-full">
                                                    {data.company}
                                                </DialogTitle>
                                                <DialogDescription>
                                                </DialogDescription>
                                            </DialogHeader>
                                            <div className="grid gap-5">
                                                <div className="w-full">
                                                    <Label
                                                        htmlFor="company"
                                                        className="text-sm text-muted-foreground font-normal"
                                                    >
                                                        Company Name
                                                    </Label>
                                                    <Input
                                                        id="company"
                                                        type="text"
                                                        value={data.company}
                                                        readOnly
                                                        placeholder="Company Name"
                                                    />
                                                </div>

                                                <div className='w-full'>
                                                    <Label htmlFor="company_name" className='text-primary'>Map With <span className='text-destructive'>*</span></Label>
                                                    <Input
                                                        id="company"
                                                        type="text"
                                                        value={companySearch} // Only bind to companySearch here
                                                        onChange={(e) => setCompanySearch(e.target.value)} // Allow user to type freely
                                                        placeholder="Company Name"
                                                        className={companyError ? 'border-red-500' : ''}
                                                    />

                                                    {companyError && <p className="text-sm text-red-500 mt-2">{companyError}</p>}

                                                    <div className='h-60 p-2 border border-muted mt-2 rounded-md overflow-scroll'>
                                                        {allCompanies?.map((company, index) => (
                                                            <p
                                                                onClick={() => {
                                                                    setSelectedCompany(company.company);
                                                                    setCompanySearch(company.company); // Set companySearch to selected company
                                                                    setCompanyError(''); // Clear error on selection
                                                                }}
                                                                className='p-2 cursor-pointer text-sm rounded-md hover:bg-muted'
                                                                key={index}
                                                            >
                                                                {company.company}
                                                            </p>
                                                        ))}
                                                    </div>
                                                </div>
                                            </div>
                                            <DialogFooter>
                                                <Button type="button" onClick={() => handleMapChange(data.company)} className='bg-cyan-600 hover:bg-cyan-700 text-white'>Save changes</Button>
                                            </DialogFooter>
                                        </DialogContent>
                                    </Dialog>

                                    {/* <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button
                                                variant={"ghost"}
                                                className="p-3 ml-2 h-5 w-5 rounded-full text-white hover:text-white hover:bg-red-600/50 bg-red-500"
                                            >
                                                <Trash />
                                            </Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    This action cannot be undone. This will permanently delete the company.
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleDelete(data._id)}>Continue</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog> */}

                                    {!data.isMapped ? <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button className='px-3 py-1 ml-3 text-sm bg-brandPrimary hover:bg-brandPrimaryDark text-white w-24'>Send Back</Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    Are you sure want to send {data.company} back to unmapped/new pool?
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleSendBack(data.company)} className="bg-brandPrimary hover:bg-brandPrimaryDark text-white">Continue</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog> :
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button className='px-3 py-1 ml-3 text-sm bg-orange-600 hover:bg-orange-700 text-white w-24'>Unmap</Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                        Do you really want to add {data.company} into unmapped database ?
                                                    </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleUnmappedCompany(data.company, data.mappedWith)} className='text-sm bg-orange-600 hover:bg-orange-700 px-3 py-1 text-white'>Continue</AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    }
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            {/* Pagination */}
            <div className='mt-10'>
                <Pagination>
                    <PaginationContent>
                        <PaginationItem className="cursor-pointer">
                            <PaginationPrevious
                                onClick={() => setPageNumber((prev) => Math.max(1, prev - 1))}
                                className={pageNumber === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                                aria-disabled={pageNumber === 1}
                            >
                                Prev
                            </PaginationPrevious>
                        </PaginationItem>

                        <PaginationItem className="cursor-pointer">
                            <PaginationLink
                                onClick={() => setPageNumber(1)}
                                isActive={pageNumber === 1}
                            >
                                1
                            </PaginationLink>
                        </PaginationItem>

                        {pageNumber > 3 && (
                            <PaginationItem>
                                <PaginationEllipsis />
                            </PaginationItem>
                        )}

                        {Array.from({ length: 3 }, (_, index) => {
                            const page = pageNumber - 1 + index;
                            if (page > 1 && page < totalPages) {
                                return (
                                    <PaginationItem className="cursor-pointer" key={page}>
                                        <PaginationLink
                                            isActive={pageNumber === page}
                                            onClick={() => setPageNumber(page)}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                );
                            }
                            return null;
                        })}

                        {pageNumber < totalPages - 2 && (
                            <PaginationItem>
                                <PaginationEllipsis />
                            </PaginationItem>
                        )}

                        {totalPages > 1 && (
                            <PaginationItem className="cursor-pointer">
                                <PaginationLink
                                    onClick={() => setPageNumber(totalPages)}
                                    isActive={pageNumber === totalPages}
                                >
                                    {totalPages}
                                </PaginationLink>
                            </PaginationItem>
                        )}

                        <PaginationItem className="cursor-pointer">
                            <PaginationNext
                                onClick={() => setPageNumber((prev) => Math.min(totalPages, prev + 1))}
                                className={pageNumber === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                                aria-disabled={pageNumber === totalPages}
                            >
                                Next
                            </PaginationNext>
                        </PaginationItem>
                    </PaginationContent>
                </Pagination>
            </div>
        </div>
    );
}

export default MapCompany;
