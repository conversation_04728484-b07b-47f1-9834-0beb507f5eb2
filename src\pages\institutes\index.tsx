import EditDialogBox from '@/components/EditDialogBox';
import { Input } from '@/components/ui/input';
import React, { useEffect, useState } from 'react';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import {
  Dialog,
  DialogContent,
  DialogClose,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import { DialogDescription } from '@radix-ui/react-dialog';
import { Label } from "@/components/ui/label";

import Spinner from '@/components/Spinner';
import axios from 'axios';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Plus, Save, View } from 'lucide-react';
import DialogBox from '@/components/DialogBox';
import { domain2 } from '@/lib/utils';
import { Institute } from '@/types';

const Institutes: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [instituteData, setInstituteData] = useState<Institute[] | undefined>(undefined);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [filters, setFilters] = useState({
    name: '',
    state: '',
    district: ''
  });

  const [formData, setFormData] = useState({
    name: '',
    aisheCode: '',
    state: '',
    district: '',
    websiteUrl: '',
    YOE: '',
    location: '',
    collegeType: '',
    universityName: '',
    universityType: '',
    administrativeMinistry: '',
    management: '',
  });

  const [singleInstitute, setSingleInstitute] = useState<Institute | null>(null);
  const [singleInstituteLoading, setSingleInstituteLoading] = useState<boolean>(false);

  const handleChange = (e: any) => {
    const { id, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  const pageSize = 50;

  const fetchInstitutes = async (page?: number) => {
    const encodedName = encodeURIComponent(filters.name || "");
    const encodedState = encodeURIComponent(filters.state || "");
    const encodedDistrict = encodeURIComponent(filters.district || "");
    const res = await axios.get(
      `${domain2}/api/mapping/v1/education-master/all-institute?page=${page}&name=${encodedName}&state=${encodedState}&district=${encodedDistrict}`,
      {
        headers: {
          "Content-Type": "application/json"
        }
      }
    );

    const fetchedInstitutes = res.data.data.institutes;
    const fetchedTotalInstitutes = res.data.data.totalInstitutes;

    setInstituteData(fetchedInstitutes);
    setTotalPages(Math.ceil(fetchedTotalInstitutes / pageSize));
  }

  const fetchSingleInstitute = async (id: string) => {
    setSingleInstituteLoading(true);
    const response = await axios.get(`${domain2}/api/mapping/v1/education-master/view-institute/${id}`);

    if (response.data.status) {
      setSingleInstitute(response.data.data);
      setSingleInstituteLoading(false);
    }
  }

  useEffect(() => {
    setLoading(true);
    fetchInstitutes(pageNumber);
    setLoading(false);
  }, [pageNumber, filters]);

  const addInstitute = async () => {
    try {
      setLoading(true);
      const res = await axios.post(
        `${domain2}/api/mapping/v1/education-master/add-institute`,
        {
          aisheCode: formData.aisheCode,
          name: formData.name,
          state: formData.state,
          district: formData.district,
          websiteUrl: formData.websiteUrl,
          YOE: formData.YOE || "-",
          location: formData.location || "-",
          collegeType: formData.collegeType || "-",
          universityName: formData.universityName || "-",
          universityType: formData.universityType || "-",
          administrativeMinistry: formData.administrativeMinistry || "",
          management: formData.management || "-",
          mappedTo: []
        },
        {
          headers: {
            "Content-Type": "application/json",
          }
        }
      );

      setFormData({
        name: '',
        aisheCode: '',
        state: '',
        district: '',
        websiteUrl: '',
        YOE: '',
        location: '',
        collegeType: '',
        universityName: '',
        universityType: '',
        administrativeMinistry: '',
        management: '',
      });

      if (res.data.status) {
        toast({
          title: "Institute Added Successfully",
          className: "bg-green-800 text-white",
        });
        // Refresh the institutes list
        fetchInstitutes(pageNumber);
      }
    } catch (error) {
      toast({
        title: "Something went wrong!!!",
        variant: "destructive",
      })
    } finally {
      setLoading(false);
    }
  };

  const deleteInstitute = async (id: string) => {
    setLoading(true);
    try {
      const res = await axios.delete(`${domain2}/api/mapping/v1/education-master/delete-institute/${id}`);
      if (res.data.status) {
        toast({
          title: "Institute Deleted Successfully",
          className: "bg-green-800 text-white",
        });
        // Refresh the institutes list
        fetchInstitutes(pageNumber);
      }
    } catch (error) {
      toast({
        title: "Something went wrong!!!",
        variant: "destructive",
      })
    } finally {
      setLoading(false);
    }
  }

  const handleSendBack = async (id: string) => {
    try {
      const response = await axios.get(`${domain2}/api/mapping/v1/education-master/view-institute/${id}`);
      if (response.data.status) {
        toast({
          title: "Institute sent back successfully",
          className: "bg-green-800 text-white"
        });
        fetchInstitutes(pageNumber);
      }
    } catch (error) {
      toast({
        title: "Something went wrong!!!",
        variant: "destructive"
      });
    }
  }

  if (loading) {
    return <Spinner />
  }

  return (
    <div className='w-full'>
      <div className="flex flex-wrap-reverse gap-3 justify-between mt-5 w-full mb-3">
        <div className='flex flex-wrap gap-3'>
          <Input
            type="text"
            className="max-w-60"
            value={filters.name}
            onChange={(e) => setFilters(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Filter by institute name"
          />
          <Input
            type="text"
            className="max-w-60"
            value={filters.state}
            onChange={(e) => setFilters(prev => ({ ...prev, state: e.target.value }))}
            placeholder="Filter by state"
          />
          <Input
            type="text"
            className="max-w-60"
            value={filters.district}
            onChange={(e) => setFilters(prev => ({ ...prev, district: e.target.value }))}
            placeholder="Filter by district"
          />
        </div>
        <div className="flex gap-3">
          <Dialog>
            <DialogTrigger asChild>
              <Button className="bg-brandPrimary hover:bg-brandPrimaryDark text-white">
                Add Institute <Plus />
              </Button>
            </DialogTrigger>

            <DialogContent className="max-w-xl max-h-[30rem] overflow-scroll py-0 px-3 sm:px-5">
              <DialogTitle className="text-2xl text-center mb-5 sticky top-0 bg-background w-full py-5">
                Add Institute
              </DialogTitle>

              <div className="space-y-5">
                <div className="flex flex-col min-[460px]:flex-row justify-between gap-5">
                  <div className="w-full">
                    <Label htmlFor="name" className="text-sm font-normal">Name <span className='text-destructive'>*</span></Label>
                    <Input id="name" type="text" value={formData.name} onChange={handleChange} placeholder="Institute Name" />
                  </div>
                  <div className="w-full">
                    <Label htmlFor="aisheCode" className="text-sm font-normal">AISHE Code <span className='text-destructive'>*</span></Label>
                    <Input id="aisheCode" type="text" value={formData.aisheCode} onChange={handleChange} placeholder="AISHE Code" />
                  </div>
                </div>

                <div className="flex justify-between flex-col min-[460px]:flex-row gap-5">
                  <div className="w-full">
                    <Label htmlFor="state" className="text-sm font-normal">State <span className='text-destructive'>*</span></Label>
                    <Input id="state" type="text" value={formData.state} onChange={handleChange} placeholder="State" />
                  </div>
                  <div className="w-full">
                    <Label htmlFor="district" className="text-sm font-normal">District <span className='text-destructive'>*</span></Label>
                    <Input id="district" type="text" value={formData.district} onChange={handleChange} placeholder="District" />
                  </div>
                </div>

                <div className="flex justify-between gap-5">
                  <div className="w-full">
                    <Label htmlFor="websiteUrl" className="text-sm font-normal">Website URL <span className='text-destructive'>*</span></Label>
                    <Input id="websiteUrl" type="text" value={formData.websiteUrl} onChange={handleChange} placeholder="Website URL" />
                  </div>
                </div>

                <div className="flex justify-between gap-5">
                  <div className="w-full">
                    <Label htmlFor="YOE" className="text-sm font-normal">Year of Establishment</Label>
                    <Input id="YOE" type="text" value={formData.YOE} onChange={handleChange} placeholder="Year of Establishment" />
                  </div>
                  <div className="w-full">
                    <Label htmlFor="location" className="text-sm font-normal">Location</Label>
                    <Input id="location" type="text" value={formData.location} onChange={handleChange} placeholder="Location" />
                  </div>
                </div>

                <div className="flex justify-between gap-5">
                  <div className="w-full">
                    <Label htmlFor="collegeType" className="text-sm font-normal">College Type</Label>
                    <Input id="collegeType" type="text" value={formData.collegeType} onChange={handleChange} placeholder="College Type" />
                  </div>
                  <div className="w-full">
                    <Label htmlFor="universityName" className="text-sm font-normal">University Name</Label>
                    <Input id="universityName" type="text" value={formData.universityName} onChange={handleChange} placeholder="University Name" />
                  </div>
                </div>

                <div className="flex justify-between gap-5">
                  <div className="w-full">
                    <Label htmlFor="universityType" className="text-sm font-normal">University Type</Label>
                    <Input id="universityType" type="text" value={formData.universityType} onChange={handleChange} placeholder="University Type" />
                  </div>
                  <div className="w-full">
                    <Label htmlFor="management" className="text-sm font-normal">Management</Label>
                    <Input id="management" type="text" value={formData.management} onChange={handleChange} placeholder="Management" />
                  </div>
                </div>

                <div className="flex justify-between gap-5">
                  <div className="w-full">
                    <Label htmlFor="administrativeMinistry" className="text-sm font-normal">Administrative Ministry</Label>
                    <Input id="administrativeMinistry" type="text" value={formData.administrativeMinistry} onChange={handleChange} placeholder="Administrative Ministry" />
                  </div>
                </div>
              </div>

              <div className="py-5 bg-background sticky bottom-0">
                <Button onClick={addInstitute} className="bg-brandPrimary hover:bg-brandPrimaryDark text-white w-full">
                  Add Institute <Save />
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div>
        {loading ? (
          <div className="flex justify-center items-center h-48">
            <Spinner />
          </div>
        ) : (
          <Table className='text-base'>
            <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
              <TableRow>
                <TableHead className='text-white'>S.No</TableHead>
                <TableHead className='text-white'>AISHE Code</TableHead>
                <TableHead className='text-white'>Institute Name</TableHead>
                <TableHead className='text-white'>State</TableHead>
                <TableHead className='text-white'>District</TableHead>
                <TableHead className='text-white'>Website</TableHead>
                <TableHead className='text-white'>College Type</TableHead>
                <TableHead className="text-white">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {instituteData?.map((data: Institute, index: number) => (
                <TableRow key={data._id}>
                  <TableCell>{(pageNumber - 1) * pageSize + index + 1}</TableCell>
                  <TableCell>{data.aisheCode}</TableCell>
                  <TableCell>{data.name}</TableCell>
                  <TableCell>{data.state}</TableCell>
                  <TableCell>{data.district}</TableCell>
                  <TableCell>
                    {data.websiteUrl && data.websiteUrl !== "-" ? (
                      <a 
                        href={`https://${data.websiteUrl}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        {data.websiteUrl}
                      </a>
                    ) : (
                      data.websiteUrl || "Not Available"
                    )}
                  </TableCell>
                  <TableCell>{data.collegeType}</TableCell>
                  <TableCell className="flex justify-end gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant={"ghost"}
                          onClick={() => fetchSingleInstitute(data._id)}
                          className="p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-green-600/50 bg-green-500"
                        >
                          <View />
                        </Button>
                      </DialogTrigger>
                      {!singleInstituteLoading ? <DialogContent className="max-w-xl max-h-[30rem] overflow-scroll py-0">
                        <DialogDescription></DialogDescription>
                        <DialogTitle className="text-2xl text-center mb-5 sticky top-0 bg-background w-full py-5">
                          Institute Details
                        </DialogTitle>

                        <div className="space-y-5">
                          <div className="flex flex-col min-[560px]:flex-row justify-between gap-5">
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                Name
                              </span>
                              <p>{singleInstitute?.name || "Not Available"}</p>
                            </div>
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                AISHE Code
                              </span>
                              <p>{singleInstitute?.aisheCode || "Not Available"}</p>
                            </div>
                          </div>

                          <div className="flex flex-col min-[560px]:flex-row justify-between gap-5">
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                State
                              </span>
                              <p>{singleInstitute?.state || "Not Available"}</p>
                            </div>
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                District
                              </span>
                              <p>{singleInstitute?.district || "Not Available"}</p>
                            </div>
                          </div>

                          <div className="w-full">
                            <span className="text-sm text-muted-foreground font-normal">
                              Website URL
                            </span>
                            <p>{singleInstitute?.websiteUrl || "Not Available"}</p>
                          </div>

                          <div className="flex flex-col min-[560px]:flex-row justify-between gap-5">
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                Year of Establishment
                              </span>
                              <p>{singleInstitute?.YOE || "Not Available"}</p>
                            </div>
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                Location
                              </span>
                              <p>{singleInstitute?.location || "Not Available"}</p>
                            </div>
                          </div>

                          <div className="flex flex-col min-[560px]:flex-row justify-between gap-5">
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                College Type
                              </span>
                              <p>{singleInstitute?.collegeType || "Not Available"}</p>
                            </div>
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                University Name
                              </span>
                              <p>{singleInstitute?.universityName || "Not Available"}</p>
                            </div>
                          </div>

                          <div className="flex flex-col min-[560px]:flex-row justify-between gap-5">
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                University Type
                              </span>
                              <p>{singleInstitute?.universityType || "Not Available"}</p>
                            </div>
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                Management
                              </span>
                              <p>{singleInstitute?.management || "Not Available"}</p>
                            </div>
                          </div>

                          <div className="w-full">
                            <span className="text-sm text-muted-foreground font-normal">
                              Administrative Ministry
                            </span>
                            <p>{singleInstitute?.administrativeMinistry || "Not Available"}</p>
                          </div>
                        </div>

                        <div className='sticky py-5 bg-background bottom-0'>
                          <DialogClose asChild className="bg-brandPrimary rounded-md py-2 bottom-5 text-center bg-background hover:bg-brandPrimaryDark cursor-pointer text-white w-full">
                            <div className="bg-background h-full w-full sticky bottom-0">
                              Ok
                            </div>
                          </DialogClose>
                        </div>
                      </DialogContent> :
                        <DialogContent className="max-w-xl max-h-[30rem] h-full overflow-scroll py-0">
                          <DialogDescription></DialogDescription>
                          <DialogTitle></DialogTitle>
                          <div className='h-full w-full'>
                            <Spinner />
                          </div>
                        </DialogContent>}
                    </Dialog>

                    <EditDialogBox 
                        type='institute' 
                        data={data} 
                        title='Edit Institute' 
                        onUpdate={(updatedInstitute) => {
                            setInstituteData(prev => 
                                prev?.map(institute => 
                                    institute._id === updatedInstitute._id ? updatedInstitute : institute
                                )
                            );
                        }}
                    />
                    <DialogBox buttonText='Delete' trigger='delete' title={`Are you sure want to delete ${data.name} ?`} description='This will permanently delete from the database' onSubmit={() => deleteInstitute(data._id)} />

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button className='px-3 py-1 ml-3 text-sm bg-brandPrimary hover:bg-brandPrimaryDark text-white'>Send Back</Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure want to send {data.name} back to unmapped/new pool?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleSendBack(data._id)}>Continue</AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>

                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>

      <div className='mt-10'>
        <Pagination>
          <PaginationContent>
            <PaginationItem className="cursor-pointer">
              <PaginationPrevious
                onClick={() => setPageNumber((prev) => Math.max(1, prev - 1))}
                className={pageNumber === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === 1}
              >
                Prev
              </PaginationPrevious>
            </PaginationItem>

            <PaginationItem className="cursor-pointer">
              <PaginationLink
                onClick={() => setPageNumber(1)}
                isActive={pageNumber === 1}
              >
                1
              </PaginationLink>
            </PaginationItem>

            {pageNumber > 3 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {Array.from({ length: 3 }, (_, index) => {
              const page = pageNumber - 1 + index;
              if (page > 1 && page < totalPages) {
                return (
                  <PaginationItem className="cursor-pointer" key={page}>
                    <PaginationLink
                      isActive={pageNumber === page}
                      onClick={() => setPageNumber(page)}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              }
              return null;
            })}

            {pageNumber < totalPages - 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {totalPages > 1 && (
              <PaginationItem className="cursor-pointer">
                <PaginationLink
                  onClick={() => setPageNumber(totalPages)}
                  isActive={pageNumber === totalPages}
                >
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            )}

            <PaginationItem className="cursor-pointer">
              <PaginationNext
                onClick={() => setPageNumber((prev) => Math.min(totalPages, prev + 1))}
                className={pageNumber === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === totalPages}
              >
                Next
              </PaginationNext>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
};

export default Institutes;