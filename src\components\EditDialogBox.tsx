import React, { useState } from "react";

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>nt,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>alogTrigger,
} from "@/components/ui/dialog";
import { Button } from "./ui/button";
import { Save, SquarePen } from "lucide-react";
import { Input } from "./ui/input";
import { DialogDescription } from "@radix-ui/react-dialog";
import { Label } from "@/components/ui/label";
import { Company } from "@/types";
import axios from "axios";
import { domain2 } from "@/lib/utils";
import { toast } from "@/hooks/use-toast";
import Spinner from "./Spinner";

interface Institute {
    _id: string;
    aisheCode: string;
    name: string;
    state: string;
    district: string;
    websiteUrl: string;
    YOE: string;
    location: string;
    collegeType: string;
    universityName: string;
    universityType: string;
    administrativeMinistry: string;
    management: string;
    mappedTo: string[];
}

interface DialogBoxProps {
    data: Company | Institute;
    title: string;
    type: "company" | "institute";
    onUpdate?: (updatedData: Institute) => void;
}

const EditDialogBox: React.FC<DialogBoxProps> = (props) => {
    const [formData, setFormData] = useState<Company | Institute>({
        ...props.data,
    });

    const [loading, setLoading] = useState<boolean>(false);
    const [isDialogOpen, setDialogOpen] = useState<boolean>(false);

    const handleInputChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        const { id, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [id]: value,
        }));
    };

    const handleSubmit = async () => {
        setLoading(true);

        const updatedFields = Object.keys(formData).reduce((acc: any, key: string) => {
            if (formData[key as keyof (Company | Institute)] !== props.data[key as keyof (Company | Institute)]) {
                acc[key] = formData[key as keyof (Company | Institute)];
            }
            return acc;
        }, {} as Partial<Company | Institute>);

        if (Object.keys(updatedFields).length === 0) {
            toast({
                title: "No changes detected",
                variant: "default",
            });
            setLoading(false);
            return;
        }

        try {
            const endpoint = props.type === "company" 
                ? `${domain2}/api/mapping/v1/company-master/update-company/${formData._id}`
                : `${domain2}/api/mapping/v1/education-master/update-institute/${formData._id}`;

            const res = await axios.patch(
                endpoint,
                updatedFields,
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );

            if (res.data.status) {
                toast({
                    title: `${props.type === "company" ? "Company" : "Institute"} Updated Successfully`,
                    className: "bg-green-800 text-white",
                });
                setDialogOpen(false);
                // Call the onUpdate callback with the updated data
                if (props.onUpdate) {
                    props.onUpdate(formData as Institute);
                }
            } else {
                toast({
                    title: "Something went wrong!!!",
                    variant: "destructive",
                });
            }
        } catch (error) {
            console.error(`Error updating ${props.type}:`, error);
            toast({
                title: `Error updating ${props.type}`,
                description: String(error),
                variant: "destructive",
            });
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return <Spinner />
    }

    return (
        <Dialog open={isDialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
                <Button
                    variant={"ghost"}
                    className="p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-cyan-600/50 bg-cyan-500"
                >
                    <SquarePen />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-xl max-h-[30rem] overflow-scroll py-0">
                <DialogDescription></DialogDescription>
                <DialogTitle className="text-2xl text-center mb-5 sticky top-0 bg-background w-full py-5">
                    {props.title}
                </DialogTitle>

                <div className="space-y-5">
                    {props.type === "company" ? (
                        <>
                            <div className="w-full">
                                <Label
                                    htmlFor="company"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Name
                                </Label>
                                <Input
                                    id="company"
                                    type="text"
                                    value={(formData as Company).company}
                                    onChange={handleInputChange}
                                    placeholder="Company Name"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="industry"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Company's Industry
                                </Label>
                                <Input
                                    id="industry"
                                    type="text"
                                    value={(formData as Company).industry}
                                    onChange={handleInputChange}
                                    placeholder="Company's Industry"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="companySize"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Size
                                </Label>
                                <Input
                                    id="companySize"
                                    type="text"
                                    value={(formData as Company).companySize}
                                    onChange={handleInputChange}
                                    placeholder="Employee Size"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="headquarters"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Company's Headquarter
                                </Label>
                                <Input
                                    id="headquarters"
                                    type="text"
                                    value={(formData as Company).headquarters}
                                    onChange={handleInputChange}
                                    placeholder="Company's Headquarter"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="website"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Website
                                </Label>
                                <Input
                                    id="website"
                                    type="text"
                                    value={(formData as Company).website}
                                    onChange={handleInputChange}
                                    placeholder="Website"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="profileUrl"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    LinkedIn Profile URL
                                </Label>
                                <Input
                                    id="profileUrl"
                                    type="text"
                                    value={(formData as Company).profileUrl}
                                    onChange={handleInputChange}
                                    placeholder="Profile URL"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="mappedTo"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Mapped Companies
                                </Label>
                                <Input
                                    id="mappedTo"
                                    type="text"
                                    value={(formData as Company).mappedTo.join(", ")}
                                    onChange={(e) =>
                                        setFormData({
                                            ...formData,
                                            mappedTo: e.target.value.split(",").map((val) => val.trim()),
                                        })
                                    }
                                    placeholder="Company Mapped With"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="specialities"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Specialities
                                </Label>
                                <textarea
                                    id="specialities"
                                    rows={4}
                                    value={(formData as Company).specialities}
                                    onChange={handleInputChange}
                                    className="bg-background resize-none p-2 border text-sm w-full rounded focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-brandPrimary"
                                    placeholder="Specialities"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="overview"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Overview
                                </Label>
                                <textarea
                                    id="overview"
                                    rows={4}
                                    value={(formData as Company).overview}
                                    onChange={handleInputChange}
                                    className="bg-background resize-none p-2 border text-sm w-full rounded focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-brandPrimary"
                                    placeholder="Overview"
                                />
                            </div>
                            <div className="w-full">
                                <Label htmlFor="companyLogo" className="text-sm font-normal">Company Logo (Optional)</Label>
                                <Input id="companyLogo" type="text" onChange={handleInputChange} value={(formData as Company).companyLogo} placeholder="Company Logo" />
                            </div>
                        </>
                    ) : (
                        <>
                            <div className="w-full">
                                <Label
                                    htmlFor="name"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Name
                                </Label>
                                <Input
                                    id="name"
                                    type="text"
                                    value={(formData as Institute).name}
                                    onChange={handleInputChange}
                                    placeholder="Institute Name"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="aisheCode"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    AISHE Code
                                </Label>
                                <Input
                                    id="aisheCode"
                                    type="text"
                                    value={(formData as Institute).aisheCode}
                                    onChange={handleInputChange}
                                    placeholder="AISHE Code"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="state"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    State
                                </Label>
                                <Input
                                    id="state"
                                    type="text"
                                    value={(formData as Institute).state}
                                    onChange={handleInputChange}
                                    placeholder="State"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="district"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    District
                                </Label>
                                <Input
                                    id="district"
                                    type="text"
                                    value={(formData as Institute).district}
                                    onChange={handleInputChange}
                                    placeholder="District"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="websiteUrl"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Website URL
                                </Label>
                                <Input
                                    id="websiteUrl"
                                    type="text"
                                    value={(formData as Institute).websiteUrl}
                                    onChange={handleInputChange}
                                    placeholder="Website URL"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="YOE"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Year of Establishment
                                </Label>
                                <Input
                                    id="YOE"
                                    type="text"
                                    value={(formData as Institute).YOE}
                                    onChange={handleInputChange}
                                    placeholder="Year of Establishment"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="location"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Location
                                </Label>
                                <Input
                                    id="location"
                                    type="text"
                                    value={(formData as Institute).location}
                                    onChange={handleInputChange}
                                    placeholder="Location"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="collegeType"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    College Type
                                </Label>
                                <Input
                                    id="collegeType"
                                    type="text"
                                    value={(formData as Institute).collegeType}
                                    onChange={handleInputChange}
                                    placeholder="College Type"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="universityName"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    University Name
                                </Label>
                                <Input
                                    id="universityName"
                                    type="text"
                                    value={(formData as Institute).universityName}
                                    onChange={handleInputChange}
                                    placeholder="University Name"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="universityType"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    University Type
                                </Label>
                                <Input
                                    id="universityType"
                                    type="text"
                                    value={(formData as Institute).universityType}
                                    onChange={handleInputChange}
                                    placeholder="University Type"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="management"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Management
                                </Label>
                                <Input
                                    id="management"
                                    type="text"
                                    value={(formData as Institute).management}
                                    onChange={handleInputChange}
                                    placeholder="Management"
                                />
                            </div>
                            <div className="w-full">
                                <Label
                                    htmlFor="administrativeMinistry"
                                    className="text-sm text-muted-foreground font-normal"
                                >
                                    Administrative Ministry
                                </Label>
                                <Input
                                    id="administrativeMinistry"
                                    type="text"
                                    value={(formData as Institute).administrativeMinistry}
                                    onChange={handleInputChange}
                                    placeholder="Administrative Ministry"
                                />
                            </div>
                        </>
                    )}
                </div>

                <div className="py-5 bg-background h-full w-full sticky bottom-0">
                    <Button
                        onClick={handleSubmit}
                        className="bg-brandPrimary hover:bg-brandPrimaryDark text-white w-full"
                    >
                        Edit <Save />
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default EditDialogBox;
