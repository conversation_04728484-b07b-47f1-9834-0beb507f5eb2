import React from 'react';

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from './ui/button';
import { ShieldAlert, SquarePen, Trash, View } from 'lucide-react';

interface DialogBoxProps {
    title: string;
    description: string;
    buttonText: string;
    className?: string;
    trigger: "view" | "edit" | "delete" | "block";
    onSubmit: () => void;
}


const DialogBox: React.FC<DialogBoxProps> = (props) => {
    return (
        <AlertDialog>
            <AlertDialogTrigger asChild>
                {["delete", "edit", "view", "block"].includes(props.trigger) && (
                    <Button
                        variant="ghost"
                        className={`p-3 h-5 w-5 rounded-full text-white hover:text-white ${props.trigger === "delete"
                                ? "hover:bg-red-600/50 bg-red-500"
                                : props.trigger === "edit"
                                    ? "hover:bg-cyan-600/50 bg-cyan-500"
                                    : props.trigger === "view"
                                        ? "hover:bg-green-600/50 bg-green-500"
                                        : "hover:bg-yellow-600/50 bg-yellow-500"
                            }`}
                    >
                        {props.trigger === "delete" && <Trash />}
                        {props.trigger === "edit" && <SquarePen />}
                        {props.trigger === "view" && <View />}
                        {props.trigger === "block" && <ShieldAlert />}
                    </Button>
                )}
            </AlertDialogTrigger>

            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>{props.title}</AlertDialogTitle>
                    <AlertDialogDescription>
                        {props.description}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                        onClick={props.onSubmit}
                        className={`text-white ${props.trigger === "delete"
                                ? "bg-red-500 hover:bg-red-600/80"
                                : props.trigger === "edit"
                                    ? "bg-cyan-500 hover:bg-cyan-600/80"
                                    : props.trigger === "view"
                                        ? "bg-green-500 hover:bg-green-600/80"
                                        : "bg-yellow-500 hover:bg-yellow-600/80"
                            } ${props.className}`}
                    >
                        {props.buttonText}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    )
}

export default DialogBox;