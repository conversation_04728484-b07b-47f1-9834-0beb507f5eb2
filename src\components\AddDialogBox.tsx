import React from 'react';

import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from './ui/button';
import { Plus } from 'lucide-react';
import { Input } from './ui/input';
import { DialogDescription } from '@radix-ui/react-dialog';

interface DialogBoxProps {
    title: string;
    placeholder: string;
    buttonText: string;
    onSubmit: () => void;
}


const AddDialogBox:React.FC<DialogBoxProps> = (props) => {


    
    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button className='bg-brandPrimary hover:bg-brandPrimaryDark text-white'>{props.buttonText}<Plus /></Button>
            </DialogTrigger>
            <DialogContent className='max-w-96'>
                <DialogDescription></DialogDescription>
                <DialogTitle className='text-2xl text-center mb-5'>{props.title}</DialogTitle>
                <Input type='text' placeholder={props.placeholder} />
                <Button onClick={props.onSubmit} className='bg-brandPrimary hover:bg-brandPrimaryDark text-white'>{props.buttonText} <Plus /></Button>
            </DialogContent>
        </Dialog>
    )
}

export default AddDialogBox;