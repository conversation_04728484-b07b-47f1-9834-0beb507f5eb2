import React, { useEffect, useState } from 'react';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import axios from 'axios';
import { convertTime, domain2 } from '@/lib/utils';
import Card from '@/components/Card';
import { toast } from '@/hooks/use-toast';
import Spinner from '@/components/Spinner';
import { Link } from 'react-router-dom';
import { Notification } from '@/types';
import { Input } from '@/components/ui/input';

const NotificationReports: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedBox, setSelectedBox] = useState<number>(1);
  const [reportType, setReportType] = useState<"send-connection-request" | "accept-connection-request" | "profile-view">("send-connection-request");
  const [notificationData, setNotificationData] = useState<Notification[]>([]);
  const [totalReadData, setTotalReadData] = useState<Notification[]>([]);
  const [totalUnseenData, setTotalUnseenData] = useState<Notification[]>([]);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [itemsPerPage, setItemPerPage] = useState<number>(10); // Define how many items per page
  const [nameFilter, setNameFilter] = useState<string>("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true); // Set loading to true before starting the request
        const res = await axios.get(`${domain2}/api/mapping/v1/notification-report/${reportType}`);

        const fetchedData = res.data.data; // Use this directly instead of relying on state
        setNotificationData(fetchedData);

        const readMessages = fetchedData.filter((data: Notification) => data.isRead);
        setTotalReadData(readMessages);

        const unseenMessages = fetchedData.filter((data: Notification) => !data.isRead);
        setTotalUnseenData(unseenMessages);

      } catch (error) {
        toast({
          title: "Something Went Wrong!!!",
          description: error as string,
          variant: "destructive",
        });
      } finally {
        setLoading(false); // Set loading to false after the request completes
      }
    };

    fetchData();
  }, [reportType]);


  // Logic for filtering based on the selectedBox
  const filteredData = React.useMemo(() => {
    let data: Notification[] = [];
    if (selectedBox === 1) data = notificationData; // Total Sent Notifications
    if (selectedBox === 2) data = totalReadData; // Total Read Notifications
    if (selectedBox === 3) data = totalUnseenData; // Total Unseen Notifications

    if (nameFilter) {
      data = data.filter(item =>
        (item.user_id.first_name && item.user_id.first_name.toLowerCase().includes(nameFilter.toLowerCase())) ||
        (item.user_id.last_name && item.user_id.last_name.toLowerCase().includes(nameFilter.toLowerCase()))
      );
    }

    return data;
  }, [selectedBox, notificationData, totalReadData, totalUnseenData, nameFilter]);

  // Logic for pagination
  const indexOfLastItem = pageNumber * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredData?.slice(indexOfFirstItem, indexOfLastItem);

  const totalPages = Math.ceil((filteredData?.length ?? 0) / itemsPerPage);

  if (loading) {
    return <Spinner />
  }

  return (
    !loading ? <div className='max-w-full w-full p-2'>
      <div className='w-fit'>
        <Select value={reportType} onValueChange={(value) => setReportType(value as "send-connection-request" | "accept-connection-request" | "profile-view")}>
          <SelectTrigger>
            <SelectValue placeholder={"Send Connection Request"} />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Select Report</SelectLabel>
              <SelectItem className='cursor-pointer' value="send-connection-request">Send Connection Request</SelectItem>
              <SelectItem className='cursor-pointer' value="accept-connection-request">Accept Connection Request</SelectItem>
              <SelectItem className='cursor-pointer' value="profile-view">Profile View</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>

      {/* Cards */}
      <div className='flex flex-col min-[476px]:flex-row gap-3 mt-5 w-full'>
        <Link to="#" onClick={() => { setSelectedBox(1); setPageNumber(1) }} className='w-full h-full'>
          <Card count={notificationData.length} title='Total Sent Notifications' className={`!w-full ${selectedBox === 1 ? "bg-sky-600 !text-white duration-300" : "duration-300 bg-sky-600/10"} border-2 border-sky-600 text-sky-600`} />
        </Link>

        <Link to="#" onClick={() => { setSelectedBox(2); setPageNumber(1) }} className='w-full h-full'>
          <Card count={totalReadData.length} title='Total Read Notifications' className={`!w-full ${selectedBox === 2 ? "bg-emerald-600 !text-white duration-300" : "duration-300 bg-emerald-600/10"} border-2 border-emerald-600 text-emerald-600`} />
        </Link>

        <Link to="#" onClick={() => { setSelectedBox(3); setPageNumber(1) }} className='w-full h-full'>
          <Card count={totalUnseenData.length} title='Total Unseen Notifications' className={`!w-full ${selectedBox === 3 ? "bg-rose-600 !text-white duration-300" : "duration-300 bg-rose-600/10"} border-2 border-rose-600 text-rose-600`} />
        </Link>
      </div>

      {/* Filters */}
      <div className='my-5 flex gap-3'>
        <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemPerPage(Number(value))}>
          <SelectTrigger className="w-fit">
            <SelectValue placeholder="Show Rows" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Select Rows</SelectLabel>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>

        {/* Filter By Name */}
        <div>
          <Input
            type="text"
            className="max-w-60"
            onChange={(e) => setNameFilter(e.target.value)}
            placeholder="Filter by name"
          />
        </div>
      </div>

      {/* Table */}
      <div className='overflow-scroll'>
        {loading ? (
          <div className="flex justify-center items-center h-48">
            <Spinner />
          </div>
        ) : (
          <Table className='text-base w-full'>
            <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
              <TableRow>
                <TableHead className='text-white'>S.No</TableHead>
                <TableHead className='text-white'>Name</TableHead>
                <TableHead className='text-white'>Body</TableHead>
                <TableHead className="text-white">Status</TableHead>
                <TableHead className="text-white">Time</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentItems?.map((data: Notification, index: number) => (
                <TableRow key={data._id}>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{data.user_id.first_name + " " + data.user_id.last_name}</TableCell>
                  <TableCell>{data.title}</TableCell>
                  <TableCell>{data.isRead ? "Seen" : "Unseen"}</TableCell>
                  <TableCell>{convertTime(data.createdAt)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>

      {/* Pagination */}
      <div className='mt-10'>
        <Pagination>
          <PaginationContent>
            {/* Previous Button */}
            <PaginationItem className="cursor-pointer">
              <PaginationPrevious
                onClick={() => setPageNumber((prev) => Math.max(1, prev - 1))}
                className={pageNumber === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === 1}
              >
                Prev
              </PaginationPrevious>
            </PaginationItem>

            {/* First page */}
            <PaginationItem className="cursor-pointer">
              <PaginationLink
                onClick={() => setPageNumber(1)}
                isActive={pageNumber === 1}
              >
                1
              </PaginationLink>
            </PaginationItem>

            {/* Ellipsis before the current range */}
            {pageNumber > 3 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Dynamic Page Numbers */}
            {Array.from({ length: 3 }, (_, index) => {
              const page = pageNumber - 1 + index;
              if (page > 1 && page < totalPages) {
                return (
                  <PaginationItem className="cursor-pointer" key={page}>
                    <PaginationLink
                      isActive={pageNumber === page}
                      onClick={() => setPageNumber(page)}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              }
              return null;
            })}

            {/* Ellipsis after the current range */}
            {pageNumber < totalPages - 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Last page */}
            {totalPages > 1 && (
              <PaginationItem className="cursor-pointer">
                <PaginationLink
                  onClick={() => setPageNumber(totalPages)}
                  isActive={pageNumber === totalPages}
                >
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            )}

            {/* Next Button */}
            <PaginationItem className="cursor-pointer">
              <PaginationNext
                onClick={() => setPageNumber((prev) => Math.min(totalPages, prev + 1))}
                className={pageNumber === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === totalPages}
              >
                Next
              </PaginationNext>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>

    </div> :
      <Spinner />
  );
}

export default NotificationReports;
