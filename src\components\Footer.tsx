import React from 'react';

const Footer: React.FC = () => {
    const currentYear = new Date().getFullYear(); // Get the current year
    const nextYear = currentYear + 1;
    const nextYearShort = nextYear.toString().slice(-2); // Get last two digits of the next year

  return (
    <footer className='p-3 2xl:p-5 text-center text-sm'>
        <p>Copyright &copy; Mapping Data {currentYear}-{nextYearShort}</p>
    </footer>
  );
}

export default Footer;