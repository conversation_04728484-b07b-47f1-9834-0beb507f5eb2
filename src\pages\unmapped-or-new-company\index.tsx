import React, { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import { MapCompanyType } from '@/types';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import Spinner from '@/components/Spinner'; // Import Spinner component for loading indicator
import axios from 'axios';
import useCompanyStore from '@/store/companyStore';
import { domain2 } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { Plus, Trash } from 'lucide-react';
import { DialogTrigger } from '@/components/ui/dialog';
import { Dialog, DialogTitle, DialogContent } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';

const UnmappedOrNewCompany: React.FC = () => {
  const intialAllCompanies = useCompanyStore(state => state.totalCompanies);
  const [loading, setLoading] = useState<boolean>(false);
  const [mapCompanyData, setMapCompanyData] = useState<MapCompanyType[] | undefined>(undefined);
  const [filteredCompanyData, setFilteredCompanyData] = useState<MapCompanyType[] | undefined>(undefined);
  const [pageNumber, setPageNumber] = useState<number>(1); // Start with page 1
  const [totalPages, setTotalPages] = useState<number>(Math.ceil(intialAllCompanies / 50)); // Default page calculation
  const [itemsPerPage, setItemPerPage] = useState<number>(10);
  const [companyFilter, setCompanyFilter] = useState<string>("");
  const [companyVariation, setCompanyVariation] = useState<string>("");

  const pageSize = 50; // Companies per page

  const fetchMapCompanies = async () => {
    setLoading(true);
    try {
      const res = await axios.post(`${domain2}/api/mapping/v1/company-master/get-all-unmapped-or-new-company`);

      const fetchedCompanies = res.data.data;
      setMapCompanyData(fetchedCompanies);
      setFilteredCompanyData(fetchedCompanies); // Set the fetched data to be initially displayed
      setTotalPages(Math.ceil(fetchedCompanies.length / pageSize));
    } catch (error) {
      console.error("Error fetching companies:", error);
    } finally {
      setLoading(false);
    }
  }

  const handleAddToUnmapped = async (companyName: string) => {
    const response = await axios.post(`${domain2}/api/mapping/v1/company-master/add-unmapped-company`, {
      "company": companyName,
      "isMapped": false,
      "mappedWith": ""
    }, {
      headers: {
        "Content-Type": "application/json"
      }
    });

    if (response.data.status) {
      toast({
        title: "Company Added to Unmapped",
        className: "bg-green-700 text-white"
      });
      fetchMapCompanies();
    }
  }

  const handleAddNewCompany = async (companyName: string) => {
    try {
      const response = await axios.post(
        `${domain2}/api/mapping/v1/company-master/add-company`,
        {
          profileUrl: "N/A",
          company: companyName,
          website: "N/A",
          industry: "N/A",
          companySize: "N/A",
          headquarters: "N/A",
          specialities: "N/A",
          overview: "N/A",
          companyLogo: "",
          mappedTo: [],
        },
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.data.status) {
        toast({
          title: "Company is added to list",
          className: "bg-green-700 text-white",
        });
        fetchMapCompanies();
      } else {
        toast({
          title: "Company is already added to list",
          className: "bg-red-800 text-white",
        });
      }
    } catch (error: any) {
      console.error("Error adding company:", error);
      toast({
        title: error.response?.data?.message || "Something went wrong",
        variant: "destructive"
      });
    }
  };


  useEffect(() => {
    fetchMapCompanies();
  }, []);

  // useEffect(() => {
  //   fetchCompanies(companySearch);
  // }, [companySearch]);

  useEffect(() => {
    if (companyFilter) {
      const filteredData = mapCompanyData?.filter(company =>
        company.company.toLowerCase().includes(companyFilter.toLowerCase())
      );
      setFilteredCompanyData(filteredData);
      // Reset to first page when filtering
      setPageNumber(1);
      // Update total pages based on filtered data
      setTotalPages(Math.ceil((filteredData?.length || 0) / itemsPerPage));
    } else {
      setFilteredCompanyData(mapCompanyData);
      // Update total pages based on all data
      setTotalPages(Math.ceil((mapCompanyData?.length || 0) / itemsPerPage));
    }
  }, [companyFilter, mapCompanyData, itemsPerPage]);

  // Update pagination when items per page changes
  useEffect(() => {
    if (filteredCompanyData) {
      setTotalPages(Math.ceil(filteredCompanyData.length / itemsPerPage));
      // If current page is now beyond total pages, reset to page 1
      if (pageNumber > Math.ceil(filteredCompanyData.length / itemsPerPage)) {
        setPageNumber(1);
      }
    }
  }, [itemsPerPage, filteredCompanyData]);

  const handleDeleteCompany = async (id: string) => {
    try {
      setLoading(true);
      const response = await axios.delete(`${domain2}/api/mapping/v1/company-master/delete-unmapped-or-new-company/${id}`);
      if (response.data.status) {
        toast({
          title: response.data.message,
          className: "bg-green-700 text-white",
        });
        fetchMapCompanies();
      } else {
        toast({
          title: response.data.message,
          className: "bg-red-700 text-white",
        });
      }
    } catch (error) {
      toast({
        title: "Something went wrong",
        className: "bg-red-700 text-white",
      });
    } finally {
      setLoading(false);
    }
  }

  const handleAddCompanyVariation = async () => {
    try {
      const response = await axios.post(`${domain2}/api/mapping/v1/company-master/add-unmapped-or-new-company`, {
        company: companyVariation,
      }, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      if (response.data.status) {
        fetchMapCompanies();
        toast({
          title: response.data.message,
          className: "bg-green-800 text-white",
        });
      } else {
        toast({
          title: response.data.message,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Something went wrong",
        variant: "destructive",
      });
    }
  }

  if (loading) {
    return <Spinner />
  }

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pageNumbers = [];
    
    // Always show first page
    pageNumbers.push(1);
    
    // Calculate range around current page
    let startPage = Math.max(2, pageNumber - 1);
    let endPage = Math.min(totalPages - 1, pageNumber + 1);
    
    // Add ellipsis indicator if needed
    if (startPage > 2) {
      pageNumbers.push('ellipsis-start');
    }
    
    // Add pages around current page
    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }
    
    // Add ellipsis indicator if needed
    if (endPage < totalPages - 1) {
      pageNumbers.push('ellipsis-end');
    }
    
    // Add last page if there's more than one page
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }
    
    return pageNumbers;
  };

  return (
    <div className='w-full'>
      <div className='flex justify-between w-full items-center mt-5 mb-3'>
        <div className="flex flex-wrap-reverse gap-3 w-full">
          <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemPerPage(Number(value))}>
            <SelectTrigger className="w-fit">
              <SelectValue placeholder="Show Rows" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Select Rows</SelectLabel>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>

          <Input
            type="text"
            className="max-w-60"
            onChange={(e) => setCompanyFilter(e.target.value)}
            placeholder="Filter by company name"
          />
        </div>

        <Dialog>
          <DialogTrigger asChild>
            <Button className="bg-brandPrimary hover:bg-brandPrimaryDark text-white">
              Add Variation <Plus />
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-sm">
            <DialogTitle className="text-2xl text-center mb-5">
              Add Company Variation
            </DialogTitle>
            <div className="space-y-4">
              <div>
                <Label htmlFor="company" className="text-sm font-normal">
                  Company Name <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="company"
                  type="text"
                  className='mt-2'
                  onChange={(e) => setCompanyVariation(e.target.value)}
                  placeholder="Enter company name"
                />
                <Button onClick={handleAddCompanyVariation} className='bg-brandPrimary hover:bg-brandPrimaryDark text-white w-full mt-5'>Add Variation</Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div>
        <Table className='text-base'>
          <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
            <TableRow>
              <TableHead className='text-white'>S.No</TableHead>
              <TableHead className='text-white'>Company Name</TableHead>
              <TableHead className="text-white text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCompanyData?.slice((pageNumber - 1) * itemsPerPage, pageNumber * itemsPerPage).map((data, index) => (
              <TableRow key={data._id}>
                <TableCell>{(pageNumber - 1) * itemsPerPage + index + 1}</TableCell>
                <TableCell>{data.company}</TableCell>
                <TableCell className='text-right flex items-center justify-end gap-x-3'>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      {/* <Button className='px-3 py-1 text-sm bg-orange-600 hover:bg-orange-700 text-white'>Add To Unmapped</Button> */}
                      <Button className='px-3 py-1 text-sm bg-green-700 hover:bg-green-800 text-white'>Add To New</Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          Do you really want to add {data.company} into unmapped database ?
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleAddNewCompany(data.company)} className='text-sm bg-green-700 hover:bg-green-800 px-3 py-1 text-white'>Continue</AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button className='px-3 py-1 text-sm bg-orange-600 hover:bg-orange-700 text-white'>Add To Unmapped</Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          Do you really want to add {data.company} into unmapped database ?
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleAddToUnmapped(data.company)} className='text-sm bg-orange-600 hover:bg-orange-700 px-3 py-1 text-white'>Continue</AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      {/* <Button className='px-3 py-1 text-sm bg-red-700 hover:bg-red-800 text-white'>Delete</Button> */}
                      <Button
                        variant={"ghost"}
                        className="p-3 ml-2 h-5 w-5 rounded-full text-white hover:text-white hover:bg-red-600/50 bg-red-500"
                      >
                        <Trash />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          Do you really want to delete {data.company} from unmapped database ?
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleDeleteCompany(data._id)} className='text-sm bg-red-700 hover:bg-red-800 px-3 py-1 text-white'>Continue</AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>

                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className='mt-10'>
        <Pagination>
          <PaginationContent>
            <PaginationItem className="cursor-pointer">
              <PaginationPrevious
                onClick={() => setPageNumber((prev) => Math.max(1, prev - 1))}
                className={pageNumber === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === 1}
              >
                Prev
              </PaginationPrevious>
            </PaginationItem>

            {getPageNumbers().map((page, index) => {
              if (page === 'ellipsis-start' || page === 'ellipsis-end') {
                return (
                  <PaginationItem key={`ellipsis-${index}`}>
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              }
              
              return (
                <PaginationItem className="cursor-pointer" key={page}>
                  <PaginationLink
                    isActive={pageNumber === page}
                    onClick={() => setPageNumber(Number(page))}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              );
            })}

            <PaginationItem className="cursor-pointer">
              <PaginationNext
                onClick={() => setPageNumber((prev) => Math.min(totalPages, prev + 1))}
                className={pageNumber === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === totalPages}
              >
                Next
              </PaginationNext>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  );
}

export default UnmappedOrNewCompany;
