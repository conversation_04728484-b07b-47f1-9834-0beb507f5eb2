import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarTrigger,
} from "@/components/ui/sidebar";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { Link, useLocation } from "react-router-dom";
import { useState } from "react";
import { dropdownGroups, regularLinks, ChevronDown, ChevronUp } from "@/constants";

export function AppSidebar() {
  const location = useLocation();
  const [isOpen, setIsOpen] = useState<boolean>(true);
  const [openDropdowns, setOpenDropdowns] = useState<{ [key: string]: boolean }>({});

  const toggleDropdown = (title: string) => {
    setOpenDropdowns(prev => ({
      ...prev,
      [title]: !prev[title]
    }));
  };

  const isActiveInGroup = (items: typeof dropdownGroups[0]['items']) => {
    return items.some(item => location.pathname === item.url);
  };

  return (
    <Sidebar collapsible="icon" className="bg-brandPrimary text-white">
      <SidebarTrigger onClick={() => setIsOpen((prev) => !prev)} className="absolute rounded-full drop-shadow-xl p-2 shadow bg-white -right-4 bottom-0 z-50 -tranlate-y-1/2 top-1/2" />

      <SidebarContent>
        <SidebarGroup className="overflow-y-scroll">
          <SidebarGroupLabel className="py-6 2xl:py-8 px-0 grid place-content-center font-light text-lg">Klout Mapping Application</SidebarGroupLabel>
          <SidebarGroupContent className="mt-10">
            <SidebarMenu>

              {/* Home Link */}
              {(() => {
                const HomeIcon = regularLinks[0].icon;
                return (
                  <Link
                    to={regularLinks[0].url}
                    title={!isOpen ? regularLinks[0].title : ""}
                    className={`flex items-center px-2 py-4 rounded hover:bg-black/20 ${location.pathname === regularLinks[0].url ? "bg-black/20" : ""
                      }`}
                  >
                    <HomeIcon size={24} className="w-16" />
                    {isOpen && <span className="font-medium">{regularLinks[0].title}</span>}
                  </Link>
                );
              })()}

              {/* Dropdown Groups */}
              {dropdownGroups.map((group) => {
                const GroupIcon = group.icon;
                return (
                  <div key={group.title} className="relative">
                    <DropdownMenu open={openDropdowns[group.title]} onOpenChange={() => toggleDropdown(group.title)}>
                      <DropdownMenuTrigger asChild>
                        <button
                          className={`flex items-center justify-between w-full px-2 py-4 rounded hover:bg-black/20 ${isActiveInGroup(group.items) ? "bg-black/20" : ""
                            }`}
                          title={!isOpen ? group.title : ""}
                        >
                          <div className="flex items-center">
                            <GroupIcon size={24} className="w-16" />
                            {isOpen && <span className="font-medium">{group.title}</span>}
                          </div>
                          {isOpen && (
                            openDropdowns[group.title] ?
                              <ChevronUp size={16} className="ml-2" /> :
                              <ChevronDown size={16} className="ml-2" />
                          )}
                        </button>
                      </DropdownMenuTrigger>
                      {isOpen && (
                        <DropdownMenuContent
                          side="right"
                          className="bg-brandPrimary border-gray-600 text-white ml-2"
                          sideOffset={5}
                        >
                          {group.items.map((item) => {
                            const ItemIcon = item.icon;
                            return (
                              <DropdownMenuItem key={item.url} asChild className="focus:bg-black/20">
                                <Link
                                  to={item.url}
                                  className={`flex items-center px-3 py-2 text-sm hover:bg-black/20 ${location.pathname === item.url ? "bg-black/20" : ""
                                    }`}
                                >
                                  <ItemIcon size={16} className="mr-2" />
                                  {item.title}
                                </Link>
                              </DropdownMenuItem>
                            );
                          })}
                        </DropdownMenuContent>
                      )}
                    </DropdownMenu>
                  </div>
                );
              })}

              {/* Regular Links (excluding Home which is already rendered) */}
              {regularLinks.slice(1).map((item) => {
                const ItemIcon = item.icon;
                return (
                  <Link
                    key={item.url}
                    to={item.url}
                    title={!isOpen ? item.title : ""}
                    className={`flex items-center px-2 py-4 rounded hover:bg-black/20 ${location.pathname === item.url ? "bg-black/20" : ""
                      }`}
                  >
                    <ItemIcon size={24} className="w-16" />
                    {isOpen && <span className="font-medium">{item.title}</span>}
                  </Link>
                );
              })}

            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
