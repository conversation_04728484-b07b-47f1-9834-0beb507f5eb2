import React from 'react';
import Navbar from './components/Navbar';
import { Outlet } from 'react-router-dom';
import { SidebarProvider } from './components/ui/sidebar';
import { AppSidebar } from './components/AppSidebar';
import Footer from './components/Footer';

const Layout: React.FC = () => {

    return (
        // <SidebarProvider className='text-brandText'>
        //     <AppSidebar />
        //     <main className='w-full flex flex-col flex-1'>
        //         <Navbar />
        //         {/* <SidebarTrigger /> */}
        //         <div className='flex-1 flex h-full min-w-full p-3 2xl:p-5'>
        //             <Outlet />
        //         </div>
        //         <Footer />
        //     </main>
        // </SidebarProvider>

        <main className='h-screen w-full border-2 !overflow-hidden flex !text-brandText'>
            <SidebarProvider className='max-w-fit overflow-hidden'>
                <AppSidebar />
            </SidebarProvider>
            <section className='flex-1 flex flex-col overflow-x-hidden'>
                <Navbar />
                <div className='h-full w-full min-h-fit p-3 2xl:p-5'>
                    <Outlet />
                </div>
                <Footer />
            </section>
        </main>
    )
}

export default Layout;