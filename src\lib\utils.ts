import axios from "axios";
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Domain Name
export const domain = import.meta.env.VITE_API;
export const domain2 = import.meta.env.VITE_APP;
export const token = localStorage.getItem('kloutMapAppToken');
export const avatarImage = "https://github.com/shadcn.png";

export const fetchSingleCompany = async (id: string) => {
  const response = await axios.get(`${domain2}/api/mapping/v1/company-master/view-company/${id}`);

  if (response.data.status) {
    return response.data.data;
  }
  else {
    return "Something Went Wrong!!!";
  }
}

export const convertTime = (createdAt: string): string => {
  const date: Date = new Date(createdAt);

  const options: Intl.DateTimeFormatOptions = {
    month: '2-digit',
    day: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  };

  const formattedDate: string = date.toLocaleString('en-US', options);
  return formattedDate;
}
