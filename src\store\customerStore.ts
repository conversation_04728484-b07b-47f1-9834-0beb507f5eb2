// import { Customer } from "@/types";
// import { create } from "zustand";
// import { persist, PersistOptions } from "zustand/middleware";

// // Define the types
// interface CustomerStore {
//     allCustomers: Customer[];
//     setAllCustomers: (customers: Customer[]) => void;
// }

// // Create the store
// const useCustomerStore = create<CustomerStore>()(
//     persist(
//         (set) => ({
//             allCustomers: [],
//             setAllCustomers: (customers: Customer[]) => set(() => ({
//                 allCustomers: customers
//             })),
//         }),
//         {
//             name: "customer-storage", // Unique name for persistence
//         } as PersistOptions<CustomerStore>
//     )
// );

// export default useCustomerStore;












import { Customer } from "@/types";
import { create } from "zustand";
import { persist, PersistOptions } from "zustand/middleware";

// Define the types
interface CustomerStore {
    allCustomers: Customer[];
    setAllCustomers: (customers: Customer[]) => void;
}

// Create the store
const useCustomerStore = create<CustomerStore>()(
    persist(
        (set) => ({
            allCustomers: [],
            setAllCustomers: (customers: Customer[]) => {
                const dataSize = new Blob([JSON.stringify(customers)]).size;
                const storageLimit = 5 * 1024 * 1024; // 5MB limit

                if (dataSize <= storageLimit) {
                    set(() => ({
                        allCustomers: customers
                    }));
                } else {
                    console.error("Data exceeds storage limit");
                }
            },
        }),
        {
            name: "customer-storage", // Unique name for persistence
        } as PersistOptions<CustomerStore>
    )
);


export default useCustomerStore;

