import Modal from '@/components/Modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { domain } from '@/lib/utils';
import useAuthStore from '@/store/authStore';
import axios from 'axios';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);

  const token: string = "";
  const { toast } = useToast();
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [message, setMessage] = useState<string>("");
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const handleLogin = async (): Promise<void> => {
    try {
      const res = await axios.post(
        `${domain}/mapping/api/login`,
        { email, password },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (res.data.status === 200) {

        // Store user data in AuthStore
        login(res.data.access_token);

        navigate("/");
        toast({
          title: "Logged In Successfully",
          className: "bg-green-800 text-white",
        });
      } else {
        setMessage(res.data.message);
        setIsModalOpen(true);
      }
    } catch (error) {
      setMessage("An unexpected error occurred.");
      setIsModalOpen(true);
    }
  };


  return (
    <div className='h-screen w-full grid place-content-center'>
      <div className='p-5 border-2 bg-muted max-w-fit mx-auto rounded-lg'>
        <h1 className='text-primary text-3xl text-center font-semibold'>Log In</h1>

        <div className='mt-10 space-y-5'>
          {/* Email */}
          <div>
            <Label htmlFor="email" className='text-primary'>Email</Label>
            <Input onChange={(e) => setEmail(e.target.value)} type='email' placeholder='<EMAIL>' className='w-80' />
          </div>

          {/* Password */}
          <div>
            <Label htmlFor="password" className='text-primary'>Password</Label>
            <Input onChange={(e) => setPassword(e.target.value)} type='password' placeholder='****' className='w-80' />
          </div>

          {/* Submit Button */}
          <Button onClick={handleLogin} className='bg-brandPrimary hover:bg-brandPrimaryDark w-full text-white'>Log In</Button>
        </div>

        <Modal
          title='Login Failed'
          description={message}
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
      </div>
    </div>
  );
};

export default Login;
