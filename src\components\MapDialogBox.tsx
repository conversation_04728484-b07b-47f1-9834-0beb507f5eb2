import React from 'react';

import {
    <PERSON><PERSON>,
    DialogContent,
    Di<PERSON>Title,
    DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from './ui/button';
import { Map } from 'lucide-react';
import { Input } from './ui/input';
import { DialogDescription } from '@radix-ui/react-dialog';

interface DialogBoxProps {
    title: string;
    placeholder: string;
    buttonText: string;
    mapPlaceholder: string;
    onSubmit: () => void;
}


const MapDialogBox: React.FC<DialogBoxProps> = (props) => {

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button className='bg-brandPrimary hover:bg-brandPrimaryDark text-white'>{props.buttonText}<Map /></Button>
            </DialogTrigger>
            <DialogContent className='max-w-96'>
                <DialogDescription></DialogDescription>
                <DialogTitle className='text-2xl text-center mb-5'>{props.title}</DialogTitle>
                <Input type='text' placeholder={props.placeholder} />
                <Input type='text' placeholder={props.mapPlaceholder} />
                <Button onClick={props.onSubmit} className='bg-brandPrimary hover:bg-brandPrimaryDark text-white'>{props.buttonText} <Map /></Button>
            </DialogContent>
        </Dialog>
    )
}

export default MapDialogBox;