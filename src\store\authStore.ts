import { domain } from '@/lib/utils';
import { User } from '@/types';
import axios from 'axios';
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface AuthState {
    isAuthenticated: boolean;
    token: string | null;
    login: (token: string) => void;
    logout: () => void;
    user: User | null;
}

const fetchUserProfile = async (): Promise<User | null> => {
    const token = localStorage.getItem('kloutMapAppToken');
    if (!token) {
        return null;
    }

    try {
        const response = await axios.post(`${domain}/mapping/api/profile`, {}, {
            headers: {
                "Authorization": `Bearer ${token}`,
                "Content-Type": "application/json"
            },
        });
        console.log(response.data);
        return response.data.user;
    } catch (error) {
        console.error('Failed to fetch user profile:', error);
        return null;
    }
}

const useAuthStore = create<AuthState>(
    persist(
        (set) => ({
            isAuthenticated: Boolean(localStorage.getItem('kloutMapAppToken')),
            token: localStorage.getItem('kloutMapAppToken'),
            user: null,
            login: async (token: string) => {
                localStorage.setItem('kloutMapAppToken', token);
                const userData = await fetchUserProfile();
                set({
                    isAuthenticated: true,
                    token,
                    user: userData,
                });
            },
            logout: () => {
                localStorage.removeItem('kloutMapAppToken');
                set({
                    isAuthenticated: false,
                    token: null,
                    user: null
                });
            },
        }),
        {
            name: 'auth-storage', // Name of the storage key in localStorage
            storage: createJSONStorage(() => localStorage), // Correct way to define storage
        }
    ) as any
);

export default useAuthStore;
