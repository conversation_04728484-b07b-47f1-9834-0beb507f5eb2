import { ApiType } from "@/types";
import { create } from "zustand";
import { persist, PersistOptions } from "zustand/middleware";

// Define the types
interface DesignationStore {
    designations: ApiType[];
    addDesignation: (designation: ApiType) => void;
    setAllDesignations: (designation: ApiType[]) => void;
    removeDesignation: (id: number) => void;
}

// Create the store
const useDesignationStore = create<DesignationStore>()(
    persist(
        (set) => ({
            designations: [],
            addDesignation: (designation: ApiType) => set((state) => ({
                designations: [...state.designations, designation],
            })),
            removeDesignation: (id: number) => set((state) => ({
                designations: state.designations.filter(designation => designation.id !== id),
            })),
            setAllDesignations: (allDesignations: ApiType[]) => set(() => ({
                designations: allDesignations
            })),
        }),
        {
            name: "company-storage", // Unique name for persistence
        } as PersistOptions<DesignationStore>
    )
);

export default useDesignationStore;
