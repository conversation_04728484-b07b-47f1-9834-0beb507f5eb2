import { Input } from '@/components/ui/input';
import React, { useEffect, useState } from 'react';
import { Designation, MapDesignationData } from '@/types';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";

import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";

import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogTrigger,
    DialogHeader,
    DialogDescription,
    DialogFooter,
} from "@/components/ui/dialog";

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";

import { Button } from '@/components/ui/button';
import { SquarePen } from 'lucide-react';
import axios from 'axios';
import { domain2, token } from '@/lib/utils';
import { Label } from '@/components/ui/label';
import Spinner from '@/components/Spinner';
import { toast } from '@/hooks/use-toast';
const MapDesignations: React.FC = () => {
    const [itemsPerPage, setItemPerPage] = useState<number>(10);
    const [loading, setLoading] = useState<boolean>(false);
    const [designationFilter, setDesignationFilter] = useState<string>("");
    const [filterType, setFilterType] = useState<string>("all");
    const [designationData, setDesignationData] = useState<MapDesignationData[] | undefined>(undefined);
    const [filteredData, setFilteredData] = useState<MapDesignationData[] | undefined>(undefined);
    const [pageNumber, setPageNumber] = useState<number>(1);
    const [totalPages, setTotalPages] = useState<number>(1);

    const [designationSearch, setDesignationSearch] = useState<string>("");
    const [designationError, setDesignationError] = useState<string>("");
    const [selectedDesignation, setSelectedDesignation] = useState<string>("");
    const [allDesignations, setAllDesignations] = useState<Designation[]>([]);


    const fetchDesignations = async () => {
        try {
            setLoading(true);
            const response = await axios.get(`${domain2}/api/mapping/v1/designation-master/get-unmapped-designation`);
            setDesignationData(response.data.data);
            setFilteredData(response.data.data);
            // Calculate total pages based on data length and items per page
            setTotalPages(Math.ceil(response.data.data.length / itemsPerPage));
        } catch (error) {
            console.error("Error fetching designations:", error)
        } finally {
            setLoading(false);
        }
    }

    const handleMapDesignation = async (designation: string) => {
        console.log("The selected designation is: ", selectedDesignation);

        try {
            setLoading(true);
            const response = await axios.post(`${domain2}/api/mapping/v1/designation-master/mapping-unmapped-designation`, {
                unmappedDesignation: designation,
                mappedDesignation: selectedDesignation
            }, {
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Bearer ${token}`
                }
            });

            if (response.data.status) {
                fetchDesignations();
                toast({
                    title: response.data.message,
                    variant: "default",
                    className: "bg-green-800 text-white"
                });
            } else {
                toast({
                    title: response.data.message,
                    variant: "destructive"
                });
            }
        } catch (error) {
            toast({
                title: "Error mapping designation",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    }


    useEffect(() => {
        fetchDesignations();
    }, []);

    useEffect(() => {
        axios.get(`${domain2}/api/mapping/v1/designation-master/all-designation?page=${1}&search=${designationSearch}`, {
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
            }
        }).then(res => {
            setAllDesignations(res.data.data.designations);
        }).catch(err => {
            console.error(err);
        });
    }, [designationSearch]);

    useEffect(() => {
        // Apply filters whenever designationFilter or filterType changes
        if (designationData) {
            let filtered = [...designationData];

            // Filter by designation name
            if (designationFilter) {
                filtered = filtered.filter(item =>
                    item.designation.toLowerCase().includes(designationFilter.toLowerCase())
                );
            }

            // Filter by mapped status
            if (filterType === "mapped") {
                filtered = filtered.filter(item => item.isMapped);
            } else if (filterType === "unmapped") {
                filtered = filtered.filter(item => !item.isMapped);
            }

            setFilteredData(filtered);
            // Reset to page 1 when filters change
            setPageNumber(1);
            // Recalculate total pages
            setTotalPages(Math.ceil(filtered.length / itemsPerPage));
        }
    }, [designationFilter, filterType, designationData]);

    useEffect(() => {
        // Recalculate total pages when itemsPerPage changes
        if (filteredData) {
            setTotalPages(Math.ceil(filteredData.length / itemsPerPage));
            // Reset to page 1 when changing items per page
            setPageNumber(1);
        }
    }, [itemsPerPage, filteredData]);

    const handleUnmapDesignation = async (designation: string, mappedWith: string) => {
        try {
            setLoading(true);
            const response = await axios.post(`${domain2}/api/mapping/v1/designation-master/unmapped-mapped-designation`, {
                designation,
                mappedWith
            }, {
                headers: {
                    "Content-Type": "application/json"
                }
            });

            if (response.data.status) {
                fetchDesignations();
                toast({
                    title: response.data.message,
                    variant: "default",
                    className: "bg-green-800 text-white"
                });
            } else {
                toast({
                    title: response.data.message,
                    variant: "destructive"
                });
            }
        } catch (error) {
            toast({
                title: "Something went wrong",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    }

    const handleSendBackDesignation = async (designation: string) => {
        try {
            setLoading(true);
            const response = await axios.post(`${domain2}/api/mapping/v1/designation-master/send-back-unmapped-to-unmapped-or-new-designation`, {
                designation
            }, {
                headers: {
                    "Content-Type": "application/json"
                }
            });

            if (response.data.status) {
                fetchDesignations();
                toast({
                    title: response.data.message,
                    variant: "default",
                    className: "bg-green-800 text-white"
                });
            } else {
                toast({
                    title: response.data.message,
                    variant: "destructive"
                });
            }
        } catch (error) {
            toast({
                title: "Something went wrong",
                variant: "destructive"
            });
        } finally {
            setLoading(false);
        }
    }

    if (loading) {
        return <div className='flex justify-center items-center h-screen'>
            <Spinner />
        </div>
    }

    // Get paginated data
    const getPaginatedData = () => {
        if (!filteredData) return [];
        const startIndex = (pageNumber - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        return filteredData.slice(startIndex, endIndex);
    };

    // Generate page numbers to display
    const getPageNumbers = () => {
        const pageNumbers = [];

        // Always show first page
        pageNumbers.push(1);

        // Calculate range around current page
        let startPage = Math.max(2, pageNumber - 1);
        let endPage = Math.min(totalPages - 1, pageNumber + 1);

        // Add ellipsis indicator if needed
        if (startPage > 2) {
            pageNumbers.push('ellipsis-start');
        }

        // Add pages around current page
        for (let i = startPage; i <= endPage; i++) {
            pageNumbers.push(i);
        }

        // Add ellipsis indicator if needed
        if (endPage < totalPages - 1) {
            pageNumbers.push('ellipsis-end');
        }

        // Add last page if there's more than one page
        if (totalPages > 1) {
            pageNumbers.push(totalPages);
        }

        return pageNumbers;
    };

    return (
        <div>
            <div className="flex justify-between items-center gap-3 mt-5 w-full mb-3">
                <div className='flex gap-3'>
                    <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemPerPage(Number(value))}>
                        <SelectTrigger className="w-fit">
                            <SelectValue placeholder="Show Rows" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectLabel>Select Rows</SelectLabel>
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="25">25</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>

                    <Select value={filterType} onValueChange={setFilterType}>
                        <SelectTrigger className="w-fit">
                            <SelectValue placeholder="Filter By Type" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectLabel>Mapped or Unmapped</SelectLabel>
                                <SelectItem value="all" className='cursor-pointer'>All</SelectItem>
                                <SelectItem value="mapped" className='cursor-pointer'>Mapped</SelectItem>
                                <SelectItem value="unmapped" className='cursor-pointer'>Unmapped</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>

                    <Input
                        type="text"
                        className="max-w-60"
                        value={designationFilter}
                        onChange={(e) => setDesignationFilter(e.target.value)}
                        placeholder="Filter by designation name"
                    />
                </div>
            </div>


            {/* Table Data */}
            <div>
                <Table className='text-base'>
                    <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
                        <TableRow>
                            <TableHead className='text-white'>S.No</TableHead>
                            <TableHead className='text-white'>Designation Name</TableHead>
                            <TableHead className='text-white'>Mapped With</TableHead>
                            <TableHead className="text-white text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {getPaginatedData()?.map((data, index) => (
                            <TableRow key={data._id}>
                                <TableCell>{(pageNumber - 1) * itemsPerPage + index + 1}</TableCell>
                                <TableCell>{data.designation}</TableCell>
                                <TableCell>{data.mappedWith}</TableCell>
                                <TableCell className='text-right flex items-center justify-end'>

                                    <Dialog>
                                        <DialogTrigger asChild>
                                            {!data.isMapped && <Button
                                                variant={"ghost"}
                                                className="p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-cyan-600/50 bg-cyan-500"
                                            >
                                                <SquarePen />
                                            </Button>}

                                        </DialogTrigger>

                                        <DialogContent className="max-w-xl overflow-scroll">
                                            <DialogHeader>
                                                <DialogTitle className="text-2xl text-center sticky top-0 bg-background w-full">
                                                    {data.designation}
                                                </DialogTitle>
                                                <DialogDescription>
                                                </DialogDescription>
                                            </DialogHeader>
                                            <div className="grid gap-5">
                                                <div className="w-full">
                                                    <Label
                                                        htmlFor="company"
                                                        className="text-sm text-muted-foreground font-normal"
                                                    >
                                                        Designation Name
                                                    </Label>
                                                    <Input
                                                        id="designation"
                                                        type="text"
                                                        value={data.designation}
                                                        readOnly
                                                        placeholder="Designation Name"
                                                    />

                                                    <div className='w-full mt-5'>
                                                        <Label htmlFor="company_name" className='text-primary'>Map With <span className='text-destructive'>*</span></Label>
                                                        <Input
                                                            id="designation"
                                                            type="text"
                                                            value={designationSearch} // Only bind to companySearch here
                                                            onChange={(e) => setDesignationSearch(e.target.value)} // Allow user to type freely
                                                            placeholder="Designation Name"
                                                            className={designationError ? 'border-red-500' : ''}
                                                        />

                                                        {designationError && <p className="text-sm text-red-500 mt-2">{designationError}</p>}

                                                        <div className='h-60 p-2 border border-muted mt-2 rounded-md overflow-scroll'>
                                                            {allDesignations?.map((designation) => (
                                                                <p
                                                                    onClick={() => {
                                                                        setSelectedDesignation(designation.designation);
                                                                        setDesignationSearch(designation.designation); // Set companySearch to selected company
                                                                        setDesignationError(''); // Clear error on selection
                                                                    }}
                                                                    className='p-2 cursor-pointer text-sm rounded-md hover:bg-muted'
                                                                    key={designation.designation}
                                                                >
                                                                    {designation.designation}
                                                                </p>
                                                            ))}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <DialogFooter>
                                                <Button type="button" onClick={() => handleMapDesignation(data.designation)} className='bg-cyan-600 hover:bg-cyan-700 text-white'>Save changes</Button>
                                            </DialogFooter>
                                        </DialogContent>
                                    </Dialog>


                                    {!data.isMapped ? <AlertDialog>
                                        <AlertDialogTrigger asChild>
                                            <Button className='px-3 py-1 ml-3 text-sm bg-brandPrimary hover:bg-brandPrimaryDark text-white w-24'>Send Back</Button>
                                        </AlertDialogTrigger>
                                        <AlertDialogContent>
                                            <AlertDialogHeader>
                                                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                <AlertDialogDescription>
                                                    Are you sure want to send {data.designation} back to unmapped/new pool?
                                                </AlertDialogDescription>
                                            </AlertDialogHeader>
                                            <AlertDialogFooter>
                                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                <AlertDialogAction onClick={() => handleSendBackDesignation(data.designation)} className="bg-brandPrimary hover:bg-brandPrimaryDark text-white">Continue</AlertDialogAction>
                                            </AlertDialogFooter>
                                        </AlertDialogContent>
                                    </AlertDialog> :
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button className='px-3 py-1 ml-3 text-sm bg-orange-600 hover:bg-orange-700 text-white w-24'>Unmap</Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                        Do you really want to add {data.designation} into unmapped database ?
                                                    </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleUnmapDesignation(data.designation, data.mappedWith)} className='text-sm bg-orange-600 hover:bg-orange-700 px-3 py-1 text-white'>Continue</AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    }
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>

            {/* Pagination */}
            <div className='mt-10'>
                <Pagination>
                    <PaginationContent>
                        <PaginationItem className="cursor-pointer">
                            <PaginationPrevious
                                onClick={() => setPageNumber((prev) => Math.max(1, prev - 1))}
                                className={pageNumber === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                                aria-disabled={pageNumber === 1}
                            >
                                Prev
                            </PaginationPrevious>
                        </PaginationItem>

                        {getPageNumbers().map((page, index) => {
                            if (page === 'ellipsis-start' || page === 'ellipsis-end') {
                                return (
                                    <PaginationItem key={`ellipsis-${index}`}>
                                        <PaginationEllipsis />
                                    </PaginationItem>
                                );
                            }

                            return (
                                <PaginationItem className="cursor-pointer" key={page}>
                                    <PaginationLink
                                        isActive={pageNumber === page}
                                        onClick={() => setPageNumber(Number(page))}
                                    >
                                        {page}
                                    </PaginationLink>
                                </PaginationItem>
                            );
                        })}

                        <PaginationItem className="cursor-pointer">
                            <PaginationNext
                                onClick={() => setPageNumber((prev) => Math.min(totalPages, prev + 1))}
                                className={pageNumber === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                                aria-disabled={pageNumber === totalPages}
                            >
                                Next
                            </PaginationNext>
                        </PaginationItem>
                    </PaginationContent>
                </Pagination>
            </div>
        </div>
    )
}

export default MapDesignations;
